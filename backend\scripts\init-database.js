#!/usr/bin/env node

/**
 * CryptoVault 数据库初始化脚本
 * 自动创建数据库、运行迁移和种子数据
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
};

// 日志函数
const log = {
  info: (msg) => console.log(colors.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(colors.green(`✓ ${msg}`)),
  warning: (msg) => console.log(colors.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(colors.red(`✗ ${msg}`)),
};

// 检查环境变量
function checkEnvironment() {
  log.info('检查环境配置...');
  
  const envPath = path.join(__dirname, '../.env');
  if (!fs.existsSync(envPath)) {
    log.error('.env 文件不存在');
    process.exit(1);
  }
  
  // 读取环境变量
  require('dotenv').config({ path: envPath });
  
  if (!process.env.DATABASE_URL) {
    log.error('DATABASE_URL 环境变量未设置');
    process.exit(1);
  }
  
  log.success('环境配置检查完成');
}

// 生成Prisma客户端
function generatePrismaClient() {
  log.info('生成Prisma客户端...');
  
  try {
    execSync('npx prisma generate', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
    });
    log.success('Prisma客户端生成完成');
  } catch (error) {
    log.error('Prisma客户端生成失败');
    console.error(error.message);
    process.exit(1);
  }
}

// 推送数据库模式
function pushDatabaseSchema() {
  log.info('推送数据库模式...');
  
  try {
    execSync('npx prisma db push', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
    });
    log.success('数据库模式推送完成');
  } catch (error) {
    log.error('数据库模式推送失败');
    console.error(error.message);
    process.exit(1);
  }
}

// 运行种子数据
function runSeedData() {
  log.info('运行种子数据...');
  
  try {
    execSync('npx tsx prisma/seed.ts', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
    });
    log.success('种子数据运行完成');
  } catch (error) {
    log.error('种子数据运行失败');
    console.error(error.message);
    process.exit(1);
  }
}

// 验证数据库
function verifyDatabase() {
  log.info('验证数据库...');
  
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    // 简单查询测试
    prisma.user.count().then(count => {
      log.success(`数据库验证完成 - 用户数量: ${count}`);
      return prisma.$disconnect();
    }).catch(error => {
      log.error('数据库验证失败');
      console.error(error.message);
      process.exit(1);
    });
  } catch (error) {
    log.error('数据库验证失败');
    console.error(error.message);
    process.exit(1);
  }
}

// 显示数据库信息
function showDatabaseInfo() {
  log.info('数据库信息:');
  console.log(`  数据库URL: ${process.env.DATABASE_URL}`);
  console.log(`  数据库类型: SQLite`);
  console.log(`  Prisma版本: ${require('../package.json').devDependencies.prisma || '未知'}`);
  
  // 显示表结构
  const schemaPath = path.join(__dirname, '../prisma/schema.prisma');
  if (fs.existsSync(schemaPath)) {
    const schema = fs.readFileSync(schemaPath, 'utf8');
    const models = schema.match(/model\s+(\w+)/g) || [];
    console.log(`  数据表数量: ${models.length}`);
    console.log(`  数据表: ${models.map(m => m.replace('model ', '')).join(', ')}`);
  }
}

// 主函数
async function main() {
  try {
    console.log(colors.blue('🗄️ CryptoVault 数据库初始化开始...\n'));
    
    // 1. 检查环境
    checkEnvironment();
    
    // 2. 显示数据库信息
    showDatabaseInfo();
    
    // 3. 生成Prisma客户端
    generatePrismaClient();
    
    // 4. 推送数据库模式
    pushDatabaseSchema();
    
    // 5. 运行种子数据
    runSeedData();
    
    // 6. 验证数据库
    verifyDatabase();
    
    console.log(colors.green('\n✅ 数据库初始化完成！'));
    console.log(colors.yellow('\n📋 下一步操作:'));
    console.log('1. 启动后端服务器: npm run dev');
    console.log('2. 查看数据库: npx prisma studio');
    console.log('3. 重置数据库: npm run db:reset');
    
  } catch (error) {
    log.error(`数据库初始化失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { main };
