{"version": 3, "file": "common.d.ts", "sourceRoot": "", "sources": ["../src/common.ts"], "names": [], "mappings": ";;AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAQrC,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAA;AAItD,OAAO,KAAK,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,SAAS,CAAA;AAChE,OAAO,KAAK,EACV,mBAAmB,EACnB,YAAY,EACZ,WAAW,EAEX,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,cAAc,EACf,MAAM,SAAS,CAAA;AAChB,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAA;AAIlD;;;;;;;GAOG;AACH,qBAAa,MAAO,SAAQ,YAAY;IACtC,QAAQ,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,CAAA;IAE5C,OAAO,CAAC,YAAY,CAAa;IACjC,OAAO,CAAC,SAAS,CAAmB;IACpC,OAAO,CAAC,KAAK,CAAe;IAC5B,OAAO,CAAC,aAAa,CAAe;IAEpC,OAAO,CAAC,gBAAgB,CAA0C;IAElE;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,MAAM,CAAC,MAAM,CACX,iBAAiB,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,WAAW,EACrD,IAAI,GAAE,gBAAqB,GAC1B,MAAM;IA4FT;;;;;OAKG;IACH,MAAM,CAAC,eAAe,CACpB,WAAW,EAAE,GAAG,EAChB,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,oBAAoB,EAAE,EAAE,cAAc,GAC3E,MAAM;IAcT;;;;OAIG;IACH,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO;IAKnD,OAAO,CAAC,MAAM,CAAC,eAAe;gBAuBlB,IAAI,EAAE,UAAU;IAmB5B;;;;;OAKG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW;IA2BvE;;;OAGG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI;IAgB9C;;;;;;;;;;;;OAYG;IACH,wBAAwB,CACtB,WAAW,EAAE,UAAU,EACvB,EAAE,CAAC,EAAE,UAAU,EACf,SAAS,CAAC,EAAE,UAAU,GACrB,MAAM;IAqGT;;;;;;;;;;;;OAYG;IACH,wBAAwB,CACtB,WAAW,EAAE,UAAU,EACvB,EAAE,CAAC,EAAE,UAAU,EACf,SAAS,CAAC,EAAE,UAAU,GACrB,MAAM;IAMT;;;;OAIG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,cAAc,GAAG,IAAI;IAQhE;;;OAGG;IACH,OAAO,CAAC,IAAI,GAAE,MAAM,EAAO;IAsB3B;;;;;;;;;;OAUG;IACH,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM;IAW1C;;;;;;OAMG;IACH,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM;IAwBjF;;;;;;OAMG;IACH,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAgBxE;;;;;;;;OAQG;IACH,YAAY,CACV,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,UAAU,EACvB,EAAE,CAAC,EAAE,UAAU,EACf,SAAS,CAAC,EAAE,UAAU,GACrB,MAAM;IAKT;;;;;;;;OAQG;IACH,cAAc,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAepC;;;;;OAKG;IACH,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI,EAAE,WAAW,EAAE,UAAU,GAAG,OAAO;IAU7F;;;;OAIG;IACH,aAAa,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;IAI/C;;;;;;OAMG;IACH,mBAAmB,CAAC,SAAS,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI,EAAE,SAAS,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO;IAe/F;;;;OAIG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO;IAIjD;;;;OAIG;IACH,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI;IAS1D,iBAAiB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI;IAS9D;;;;OAIG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAapC;;;;OAIG;IACH,WAAW,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI;IASxD;;;;;;OAMG;IACH,eAAe,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO;IAO/E;;;;OAIG;IACH,4BAA4B,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI;IA4CzE;;;;;OAKG;IACH,iBAAiB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI;IA+B9D;;;;;;OAMG;IACH,mBAAmB,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO;IAQnF;;;;;OAKG;IACH,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,WAAW,EAAE,MAAM;IAkC9D;;;;OAIG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM;IAiBpE;;;;OAIG;IACH,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,cAAc,GAAG,IAAI;IAO5D;;;;OAIG;IACH,aAAa,CAAC,WAAW,EAAE,MAAM;IAYjC;;;OAGG;IACH,OAAO,IAAI,kBAAkB;IAI7B;;;OAGG;IACH,SAAS,IAAI,cAAc,EAAE;IAI7B;;;OAGG;IACH,cAAc,IAAI,mBAAmB,EAAE;IAIvC;;;OAGG;IACH,WAAW,IAAI,MAAM,EAAE;IAIvB;;;OAGG;IACH,QAAQ,IAAI,MAAM,GAAG,QAAQ;IAI7B;;;OAGG;IACH,OAAO,IAAI,MAAM;IAIjB;;;OAGG;IACH,SAAS,IAAI,MAAM;IAInB;;;OAGG;IACH,SAAS,IAAI,MAAM;IAInB;;;OAGG;IACH,IAAI,IAAI,MAAM,EAAE;IAIhB;;;;;OAKG;IACH,aAAa,IAAI,MAAM,GAAG,aAAa;IAavC;;;;;;;;OAQG;IACH,kBAAkB,IAAI,MAAM,GAAG,kBAAkB;IAajD;;;;;;;;;;;;OAYG;IACH,eAAe,IAAI;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,YAAY,GAAG,YAAY,GAAG,YAAY,CAAA;KAAE;IAgBhF;;OAEG;IACH,IAAI,IAAI,MAAM;IAMd,MAAM,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE,GAAG,YAAY;CAgBzE"}