#!/usr/bin/env node

/**
 * Node.js升级验证脚本
 * 检查升级后的环境是否正常
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
};

const log = {
  info: (msg) => console.log(colors.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(colors.green(`✓ ${msg}`)),
  warning: (msg) => console.log(colors.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(colors.red(`✗ ${msg}`)),
};

function checkNodeVersion() {
  log.info('检查Node.js版本...');
  
  const version = process.version;
  const arch = process.arch;
  const platform = process.platform;
  
  console.log(`  版本: ${version}`);
  console.log(`  架构: ${arch}`);
  console.log(`  平台: ${platform}`);
  
  if (arch !== 'x64') {
    log.error('Node.js架构不是64位，请重新安装64位版本');
    return false;
  }
  
  const majorVersion = parseInt(version.slice(1).split('.')[0]);
  if (majorVersion < 18) {
    log.warning('Node.js版本较低，建议使用18+版本');
  }
  
  log.success('Node.js版本检查通过');
  return true;
}

function checkNpmVersion() {
  log.info('检查npm版本...');
  
  try {
    const npmVersion = execSync('npm -v', { encoding: 'utf8' }).trim();
    console.log(`  npm版本: ${npmVersion}`);
    log.success('npm版本检查通过');
    return true;
  } catch (error) {
    log.error('npm不可用');
    return false;
  }
}

function checkProjectStructure() {
  log.info('检查项目结构...');
  
  const requiredPaths = [
    'frontend/package.json',
    'backend/package.json',
    'backend/prisma/schema.prisma',
    'PROJECT_PLAN.md',
    'DEVELOPMENT_PROGRESS.md',
  ];
  
  let allExists = true;
  
  for (const filePath of requiredPaths) {
    if (fs.existsSync(filePath)) {
      console.log(`  ✓ ${filePath}`);
    } else {
      console.log(`  ✗ ${filePath}`);
      allExists = false;
    }
  }
  
  if (allExists) {
    log.success('项目结构检查通过');
  } else {
    log.error('项目结构不完整');
  }
  
  return allExists;
}

function reinstallDependencies() {
  log.info('重新安装项目依赖...');
  
  // 前端依赖
  try {
    log.info('安装前端依赖...');
    if (fs.existsSync('frontend/node_modules')) {
      execSync('rm -rf node_modules package-lock.json', { 
        cwd: 'frontend',
        stdio: 'inherit' 
      });
    }
    execSync('npm install', { 
      cwd: 'frontend',
      stdio: 'inherit' 
    });
    log.success('前端依赖安装完成');
  } catch (error) {
    log.error('前端依赖安装失败');
    return false;
  }
  
  // 后端依赖
  try {
    log.info('安装后端依赖...');
    if (fs.existsSync('backend/node_modules')) {
      execSync('rm -rf node_modules package-lock.json', { 
        cwd: 'backend',
        stdio: 'inherit' 
      });
    }
    execSync('npm install', { 
      cwd: 'backend',
      stdio: 'inherit' 
    });
    log.success('后端依赖安装完成');
  } catch (error) {
    log.error('后端依赖安装失败');
    return false;
  }
  
  return true;
}

function setupPrisma() {
  log.info('配置Prisma...');
  
  try {
    // 生成Prisma客户端
    execSync('npx prisma generate', { 
      cwd: 'backend',
      stdio: 'inherit' 
    });
    log.success('Prisma客户端生成完成');
    
    // 推送数据库模式
    execSync('npx prisma db push', { 
      cwd: 'backend',
      stdio: 'inherit' 
    });
    log.success('数据库模式推送完成');
    
    return true;
  } catch (error) {
    log.error('Prisma配置失败');
    console.error(error.message);
    return false;
  }
}

function testFrontend() {
  log.info('测试前端构建...');
  
  try {
    execSync('npm run build', { 
      cwd: 'frontend',
      stdio: 'inherit',
      timeout: 60000 
    });
    log.success('前端构建测试通过');
    return true;
  } catch (error) {
    log.error('前端构建测试失败');
    return false;
  }
}

function testBackend() {
  log.info('测试后端编译...');
  
  try {
    execSync('npm run build', { 
      cwd: 'backend',
      stdio: 'inherit',
      timeout: 30000 
    });
    log.success('后端编译测试通过');
    return true;
  } catch (error) {
    log.error('后端编译测试失败');
    return false;
  }
}

async function main() {
  console.log(colors.blue('🔍 Node.js升级验证开始...\n'));
  
  let allPassed = true;
  
  // 基础检查
  allPassed &= checkNodeVersion();
  allPassed &= checkNpmVersion();
  allPassed &= checkProjectStructure();
  
  if (!allPassed) {
    log.error('基础检查失败，请先解决问题');
    process.exit(1);
  }
  
  // 重新安装依赖
  allPassed &= reinstallDependencies();
  
  if (!allPassed) {
    log.error('依赖安装失败');
    process.exit(1);
  }
  
  // 配置Prisma
  allPassed &= setupPrisma();
  
  // 测试构建
  allPassed &= testFrontend();
  allPassed &= testBackend();
  
  if (allPassed) {
    console.log(colors.green('\n✅ Node.js升级验证完成！'));
    console.log(colors.yellow('\n📋 下一步操作:'));
    console.log('1. 启动前端: cd frontend && npm run dev');
    console.log('2. 启动后端: cd backend && npm run dev');
    console.log('3. 初始化数据库: cd backend && npm run db:seed');
    console.log('4. 查看数据库: cd backend && npx prisma studio');
  } else {
    console.log(colors.red('\n❌ 验证过程中发现问题，请检查错误信息'));
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    log.error(`验证失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main };
