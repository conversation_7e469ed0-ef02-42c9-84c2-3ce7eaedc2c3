#!/usr/bin/env node

/**
 * CryptoVault 项目初始化脚本
 * 自动创建项目结构和基础配置文件
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..');

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
};

// 日志函数
const log = {
  info: (msg) => console.log(colors.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(colors.green(`✓ ${msg}`)),
  warning: (msg) => console.log(colors.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(colors.red(`✗ ${msg}`)),
};

// 创建目录结构
const directories = [
  // 前端目录
  'frontend/app/(dashboard)',
  'frontend/app/[locale]',
  'frontend/app/api',
  'frontend/components/ui',
  'frontend/components/web3',
  'frontend/components/layout',
  'frontend/components/features',
  'frontend/lib/utils',
  'frontend/lib/hooks',
  'frontend/lib/contexts',
  'frontend/lib/types',
  'frontend/lib/constants',
  'frontend/lib/themes',
  'frontend/lib/i18n',
  'frontend/lib/web3',
  'frontend/styles/themes',
  'frontend/messages',
  'frontend/public/images',
  'frontend/public/icons',
  
  // 后端目录
  'backend/src/routes/auth',
  'backend/src/routes/users',
  'backend/src/routes/tokens',
  'backend/src/routes/admin',
  'backend/src/controllers',
  'backend/src/services',
  'backend/src/models',
  'backend/src/middleware',
  'backend/src/utils',
  'backend/src/types',
  'backend/prisma/migrations',
  'backend/tests/unit',
  'backend/tests/integration',
  'backend/locales',
  
  // 共享目录
  'shared/types',
  'shared/constants',
  'shared/utils',
  
  // 其他目录
  'contracts',
  'debug',
  'docs/api',
  'docs/guides',
  'scripts/deploy',
  'scripts/build',
];

// 创建目录
function createDirectories() {
  log.info('创建项目目录结构...');
  
  directories.forEach(dir => {
    const fullPath = path.join(ROOT_DIR, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      log.success(`创建目录: ${dir}`);
    }
  });
}

// 创建基础配置文件
function createConfigFiles() {
  log.info('创建配置文件...');
  
  // 前端 package.json
  const frontendPackageJson = {
    name: 'cryptovault-frontend',
    version: '0.1.0',
    private: true,
    scripts: {
      dev: 'next dev',
      build: 'next build',
      start: 'next start',
      lint: 'next lint',
      test: 'vitest',
      'test:e2e': 'playwright test',
      'type-check': 'tsc --noEmit',
    },
    dependencies: {
      'next': '^14.0.0',
      'react': '^18.0.0',
      'react-dom': '^18.0.0',
      'typescript': '^5.0.0',
      '@types/node': '^20.0.0',
      '@types/react': '^18.0.0',
      '@types/react-dom': '^18.0.0',
      'tailwindcss': '^3.3.0',
      'autoprefixer': '^10.4.0',
      'postcss': '^8.4.0',
      'framer-motion': '^10.16.0',
      'next-intl': '^3.0.0',
      'zustand': '^4.4.0',
      '@radix-ui/react-dialog': '^1.0.0',
      '@radix-ui/react-dropdown-menu': '^2.0.0',
      '@radix-ui/react-select': '^2.0.0',
      'wagmi': '^1.4.0',
      'viem': '^1.19.0',
      '@wagmi/core': '^1.4.0',
      '@wagmi/connectors': '^3.1.0',
    },
    devDependencies: {
      'eslint': '^8.0.0',
      'eslint-config-next': '^14.0.0',
      'prettier': '^3.0.0',
      'vitest': '^1.0.0',
      '@vitejs/plugin-react': '^4.0.0',
      'playwright': '^1.40.0',
      '@playwright/test': '^1.40.0',
    },
  };
  
  // 后端 package.json
  const backendPackageJson = {
    name: 'cryptovault-backend',
    version: '0.1.0',
    private: true,
    scripts: {
      dev: 'nodemon src/index.ts',
      build: 'tsc',
      start: 'node dist/index.js',
      test: 'jest',
      'test:watch': 'jest --watch',
      'db:generate': 'prisma generate',
      'db:push': 'prisma db push',
      'db:migrate': 'prisma migrate dev',
      'db:seed': 'tsx prisma/seed.ts',
    },
    dependencies: {
      'express': '^4.18.0',
      'cors': '^2.8.0',
      'helmet': '^7.1.0',
      'morgan': '^1.10.0',
      'dotenv': '^16.3.0',
      '@prisma/client': '^5.6.0',
      'bcryptjs': '^2.4.0',
      'jsonwebtoken': '^9.0.0',
      'express-rate-limit': '^7.1.0',
      'express-validator': '^7.0.0',
      'winston': '^3.11.0',
    },
    devDependencies: {
      '@types/express': '^4.17.0',
      '@types/cors': '^2.8.0',
      '@types/morgan': '^1.9.0',
      '@types/bcryptjs': '^2.4.0',
      '@types/jsonwebtoken': '^9.0.0',
      '@types/node': '^20.0.0',
      'typescript': '^5.0.0',
      'nodemon': '^3.0.0',
      'tsx': '^4.6.0',
      'prisma': '^5.6.0',
      'jest': '^29.7.0',
      '@types/jest': '^29.5.0',
      'supertest': '^6.3.0',
      '@types/supertest': '^6.0.0',
    },
  };
  
  // 写入 package.json 文件
  fs.writeFileSync(
    path.join(ROOT_DIR, 'frontend/package.json'),
    JSON.stringify(frontendPackageJson, null, 2)
  );
  log.success('创建前端 package.json');
  
  fs.writeFileSync(
    path.join(ROOT_DIR, 'backend/package.json'),
    JSON.stringify(backendPackageJson, null, 2)
  );
  log.success('创建后端 package.json');
}

// 创建基础文件
function createBaseFiles() {
  log.info('创建基础文件...');
  
  // 前端 Next.js 配置
  const nextConfig = `/** @type {import('next').NextConfig} */
const withNextIntl = require('next-intl/plugin')('./lib/i18n/config.ts');

const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
  },
  webpack: (config) => {
    config.resolve.fallback = { fs: false, net: false, tls: false };
    return config;
  },
};

module.exports = withNextIntl(nextConfig);
`;
  
  // Tailwind 配置
  const tailwindConfig = `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        background: 'var(--color-background)',
        surface: 'var(--color-surface)',
        primary: 'var(--color-primary)',
        'text-primary': 'var(--color-text-primary)',
        'text-secondary': 'var(--color-text-secondary)',
        border: 'var(--color-border)',
        success: 'var(--color-success)',
        warning: 'var(--color-warning)',
        error: 'var(--color-error)',
      },
      borderRadius: {
        'theme-sm': 'var(--radius-sm)',
        'theme-md': 'var(--radius-md)',
        'theme-lg': 'var(--radius-lg)',
        'theme-xl': 'var(--radius-xl)',
      },
      boxShadow: {
        'theme-sm': 'var(--shadow-sm)',
        'theme-md': 'var(--shadow-md)',
        'theme-lg': 'var(--shadow-lg)',
      },
    },
  },
  plugins: [],
};
`;
  
  // TypeScript 配置
  const tsConfig = `{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*"],
      "@/lib/*": ["./lib/*"],
      "@/styles/*": ["./styles/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
`;
  
  // 写入配置文件
  fs.writeFileSync(path.join(ROOT_DIR, 'frontend/next.config.js'), nextConfig);
  fs.writeFileSync(path.join(ROOT_DIR, 'frontend/tailwind.config.js'), tailwindConfig);
  fs.writeFileSync(path.join(ROOT_DIR, 'frontend/tsconfig.json'), tsConfig);
  
  log.success('创建前端配置文件');
  
  // 后端 TypeScript 配置
  const backendTsConfig = `{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "lib": ["es2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
`;
  
  fs.writeFileSync(path.join(ROOT_DIR, 'backend/tsconfig.json'), backendTsConfig);
  log.success('创建后端配置文件');
}

// 创建环境变量文件
function createEnvFiles() {
  log.info('创建环境变量文件...');
  
  const frontendEnv = `# 前端环境变量
NEXT_PUBLIC_APP_NAME=CryptoVault
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WEB3_PROJECT_ID=your_walletconnect_project_id
NEXT_PUBLIC_DEFAULT_CHAIN_ID=1
NEXT_PUBLIC_ENABLE_DEBUG=true
`;
  
  const backendEnv = `# 后端环境变量
NODE_ENV=development
PORT=3001
DATABASE_URL="file:./dev.db"
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
`;
  
  fs.writeFileSync(path.join(ROOT_DIR, 'frontend/.env.local'), frontendEnv);
  fs.writeFileSync(path.join(ROOT_DIR, 'backend/.env'), backendEnv);
  
  log.success('创建环境变量文件');
}

// 创建 README 文件
function createReadme() {
  const readme = `# CryptoVault - Web3 DApp Platform

一个现代化的Web3 DApp平台，提供代币管理、NFT交易、质押挖矿等功能。

## 快速开始

### 前端开发
\`\`\`bash
cd frontend
npm install
npm run dev
\`\`\`

### 后端开发
\`\`\`bash
cd backend
npm install
npm run dev
\`\`\`

### 调试系统
访问 http://localhost:3000/debug 进入调试面板

## 项目特性

- 🎨 可配置的主题系统
- 🌍 多语言支持 (中英日韩)
- 📱 移动端友好设计
- 🔧 完整的调试工具
- ⚡ 高性能优化
- 🔒 安全性保障

## 技术栈

- **前端**: Next.js 14, TypeScript, Tailwind CSS
- **后端**: Node.js, Express, Prisma
- **Web3**: Wagmi, Viem
- **数据库**: SQLite (开发) / PostgreSQL (生产)

## 文档

- [项目规划](./PROJECT_PLAN.md)
- [开发进度](./DEVELOPMENT_PROGRESS.md)
- [主题系统](./THEME_SYSTEM_DESIGN.md)
- [多语言系统](./I18N_SYSTEM_DESIGN.md)
- [开发框架](./DEVELOPMENT_FRAMEWORK.md)
- [调试系统](./DEBUG_SYSTEM_DESIGN.md)

## 许可证

MIT License
`;
  
  fs.writeFileSync(path.join(ROOT_DIR, 'README.md'), readme);
  log.success('创建 README.md');
}

// 主函数
async function main() {
  try {
    console.log(colors.blue('🚀 CryptoVault 项目初始化开始...\n'));
    
    createDirectories();
    createConfigFiles();
    createBaseFiles();
    createEnvFiles();
    createReadme();
    
    console.log(colors.green('\n✅ 项目初始化完成！'));
    console.log(colors.yellow('\n📋 下一步操作:'));
    console.log('1. cd frontend && npm install');
    console.log('2. cd backend && npm install');
    console.log('3. 配置环境变量');
    console.log('4. 启动开发服务器');
    console.log('\n📖 查看文档了解更多信息');
    
  } catch (error) {
    log.error(`初始化失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { main };
`;
