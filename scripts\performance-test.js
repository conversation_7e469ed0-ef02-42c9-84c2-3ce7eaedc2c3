#!/usr/bin/env node

/**
 * CryptoVault 性能测试脚本
 * 测试网站加载速度、API响应时间等关键性能指标
 */

const puppeteer = require('puppeteer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  baseUrl: process.env.TEST_URL || 'http://localhost:3000',
  apiUrl: process.env.API_URL || 'http://localhost:3001',
  outputDir: './performance-reports',
  iterations: 3, // 测试次数
};

// 颜色输出
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

// 日志函数
const log = {
  info: (msg) => console.log(colors.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(colors.green(`✓ ${msg}`)),
  warning: (msg) => console.log(colors.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(colors.red(`✗ ${msg}`)),
  test: (msg) => console.log(colors.cyan(`🧪 ${msg}`)),
};

// 创建输出目录
function ensureOutputDir() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
}

// 页面性能测试
async function testPagePerformance(url, pageName) {
  log.test(`测试页面性能: ${pageName}`);
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });
  
  const results = [];
  
  for (let i = 0; i < CONFIG.iterations; i++) {
    const page = await browser.newPage();
    
    // 启用性能监控
    await page.setCacheEnabled(false);
    
    const startTime = Date.now();
    
    // 导航到页面
    const response = await page.goto(url, {
      waitUntil: 'networkidle2',
      timeout: 30000,
    });
    
    const loadTime = Date.now() - startTime;
    
    // 获取性能指标
    const metrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      const paint = performance.getEntriesByType('paint');
      
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
        transferSize: navigation.transferSize,
        encodedBodySize: navigation.encodedBodySize,
        decodedBodySize: navigation.decodedBodySize,
      };
    });
    
    // Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        let lcp = 0;
        let fid = 0;
        let cls = 0;
        
        // LCP
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          lcp = entries[entries.length - 1].startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // CLS
        new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            if (!entry.hadRecentInput) {
              cls += entry.value;
            }
          }
        }).observe({ entryTypes: ['layout-shift'] });
        
        setTimeout(() => {
          resolve({ lcp, fid, cls });
        }, 2000);
      });
    });
    
    results.push({
      iteration: i + 1,
      loadTime,
      statusCode: response.status(),
      metrics,
      vitals,
    });
    
    await page.close();
    log.info(`第${i + 1}次测试完成: ${loadTime}ms`);
  }
  
  await browser.close();
  
  // 计算平均值
  const avgResult = {
    pageName,
    url,
    avgLoadTime: Math.round(results.reduce((sum, r) => sum + r.loadTime, 0) / results.length),
    avgDomContentLoaded: Math.round(results.reduce((sum, r) => sum + r.metrics.domContentLoaded, 0) / results.length),
    avgFirstPaint: Math.round(results.reduce((sum, r) => sum + r.metrics.firstPaint, 0) / results.length),
    avgFirstContentfulPaint: Math.round(results.reduce((sum, r) => sum + r.metrics.firstContentfulPaint, 0) / results.length),
    avgLCP: Math.round(results.reduce((sum, r) => sum + r.vitals.lcp, 0) / results.length),
    avgCLS: (results.reduce((sum, r) => sum + r.vitals.cls, 0) / results.length).toFixed(3),
    avgTransferSize: Math.round(results.reduce((sum, r) => sum + r.metrics.transferSize, 0) / results.length),
    results,
  };
  
  return avgResult;
}

// API性能测试
async function testApiPerformance() {
  log.test('测试API性能');
  
  const endpoints = [
    { name: 'Health Check', url: '/api/health', method: 'GET' },
    { name: 'User Profile', url: '/api/users/profile', method: 'GET' },
    { name: 'Token Info', url: '/api/tokens/info', method: 'GET' },
    { name: 'Staking Pools', url: '/api/staking/pools', method: 'GET' },
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    log.info(`测试API: ${endpoint.name}`);
    
    const endpointResults = [];
    
    for (let i = 0; i < CONFIG.iterations; i++) {
      const startTime = Date.now();
      
      try {
        const response = await axios({
          method: endpoint.method,
          url: `${CONFIG.apiUrl}${endpoint.url}`,
          timeout: 10000,
        });
        
        const responseTime = Date.now() - startTime;
        
        endpointResults.push({
          iteration: i + 1,
          responseTime,
          statusCode: response.status,
          dataSize: JSON.stringify(response.data).length,
          success: true,
        });
        
      } catch (error) {
        const responseTime = Date.now() - startTime;
        
        endpointResults.push({
          iteration: i + 1,
          responseTime,
          statusCode: error.response?.status || 0,
          error: error.message,
          success: false,
        });
      }
    }
    
    // 计算平均值
    const successfulResults = endpointResults.filter(r => r.success);
    const avgResponseTime = successfulResults.length > 0 
      ? Math.round(successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length)
      : 0;
    
    results.push({
      name: endpoint.name,
      url: endpoint.url,
      method: endpoint.method,
      avgResponseTime,
      successRate: (successfulResults.length / CONFIG.iterations * 100).toFixed(1),
      results: endpointResults,
    });
    
    log.info(`${endpoint.name}: ${avgResponseTime}ms (${successfulResults.length}/${CONFIG.iterations} 成功)`);
  }
  
  return results;
}

// 资源大小分析
async function analyzeResourceSizes() {
  log.test('分析资源大小');
  
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // 监听网络请求
  const resources = [];
  
  page.on('response', async (response) => {
    const url = response.url();
    const headers = response.headers();
    
    resources.push({
      url,
      type: response.request().resourceType(),
      status: response.status(),
      size: parseInt(headers['content-length'] || '0'),
      contentType: headers['content-type'] || '',
      cached: response.fromCache(),
    });
  });
  
  await page.goto(CONFIG.baseUrl, { waitUntil: 'networkidle2' });
  
  await browser.close();
  
  // 分析资源
  const analysis = {
    totalRequests: resources.length,
    totalSize: resources.reduce((sum, r) => sum + r.size, 0),
    byType: {},
    largestResources: resources
      .sort((a, b) => b.size - a.size)
      .slice(0, 10)
      .map(r => ({
        url: r.url.split('/').pop(),
        type: r.type,
        size: r.size,
        sizeKB: Math.round(r.size / 1024),
      })),
  };
  
  // 按类型分组
  resources.forEach(resource => {
    if (!analysis.byType[resource.type]) {
      analysis.byType[resource.type] = { count: 0, size: 0 };
    }
    analysis.byType[resource.type].count++;
    analysis.byType[resource.type].size += resource.size;
  });
  
  return analysis;
}

// 生成报告
function generateReport(pageResults, apiResults, resourceAnalysis) {
  const timestamp = new Date().toISOString();
  
  const report = {
    timestamp,
    summary: {
      avgPageLoadTime: Math.round(pageResults.reduce((sum, p) => sum + p.avgLoadTime, 0) / pageResults.length),
      avgApiResponseTime: Math.round(apiResults.reduce((sum, a) => sum + a.avgResponseTime, 0) / apiResults.length),
      totalResourceSize: Math.round(resourceAnalysis.totalSize / 1024), // KB
      totalRequests: resourceAnalysis.totalRequests,
    },
    pages: pageResults,
    apis: apiResults,
    resources: resourceAnalysis,
    recommendations: generateRecommendations(pageResults, apiResults, resourceAnalysis),
  };
  
  // 保存JSON报告
  const jsonPath = path.join(CONFIG.outputDir, `performance-report-${Date.now()}.json`);
  fs.writeFileSync(jsonPath, JSON.stringify(report, null, 2));
  
  // 生成HTML报告
  const htmlReport = generateHtmlReport(report);
  const htmlPath = path.join(CONFIG.outputDir, `performance-report-${Date.now()}.html`);
  fs.writeFileSync(htmlPath, htmlReport);
  
  return { jsonPath, htmlPath, report };
}

// 生成优化建议
function generateRecommendations(pageResults, apiResults, resourceAnalysis) {
  const recommendations = [];
  
  // 页面加载时间建议
  const avgLoadTime = pageResults.reduce((sum, p) => sum + p.avgLoadTime, 0) / pageResults.length;
  if (avgLoadTime > 3000) {
    recommendations.push({
      type: 'critical',
      category: 'Page Load',
      message: `平均页面加载时间 ${Math.round(avgLoadTime)}ms 过长，建议优化代码分割和资源压缩`,
    });
  }
  
  // API响应时间建议
  const slowApis = apiResults.filter(api => api.avgResponseTime > 500);
  if (slowApis.length > 0) {
    recommendations.push({
      type: 'warning',
      category: 'API Performance',
      message: `${slowApis.length} 个API响应时间超过500ms，建议添加缓存或优化查询`,
    });
  }
  
  // 资源大小建议
  if (resourceAnalysis.totalSize > 2 * 1024 * 1024) { // 2MB
    recommendations.push({
      type: 'warning',
      category: 'Resource Size',
      message: `总资源大小 ${Math.round(resourceAnalysis.totalSize / 1024 / 1024)}MB 过大，建议压缩图片和代码`,
    });
  }
  
  return recommendations;
}

// 生成HTML报告
function generateHtmlReport(report) {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>CryptoVault 性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: white; border-radius: 4px; }
        .recommendations { margin: 20px 0; }
        .recommendation { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .critical { background: #ffebee; border-left: 4px solid #f44336; }
        .warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .info { background: #e3f2fd; border-left: 4px solid #2196f3; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>CryptoVault 性能测试报告</h1>
    <p>生成时间: ${report.timestamp}</p>
    
    <div class="summary">
        <h2>性能概览</h2>
        <div class="metric">
            <strong>平均页面加载时间</strong><br>
            ${report.summary.avgPageLoadTime}ms
        </div>
        <div class="metric">
            <strong>平均API响应时间</strong><br>
            ${report.summary.avgApiResponseTime}ms
        </div>
        <div class="metric">
            <strong>总资源大小</strong><br>
            ${report.summary.totalResourceSize}KB
        </div>
        <div class="metric">
            <strong>总请求数</strong><br>
            ${report.summary.totalRequests}
        </div>
    </div>
    
    <div class="recommendations">
        <h2>优化建议</h2>
        ${report.recommendations.map(rec => `
            <div class="recommendation ${rec.type}">
                <strong>${rec.category}</strong>: ${rec.message}
            </div>
        `).join('')}
    </div>
    
    <h2>页面性能详情</h2>
    <table>
        <tr>
            <th>页面</th>
            <th>加载时间</th>
            <th>首次绘制</th>
            <th>首次内容绘制</th>
            <th>LCP</th>
            <th>CLS</th>
        </tr>
        ${report.pages.map(page => `
            <tr>
                <td>${page.pageName}</td>
                <td>${page.avgLoadTime}ms</td>
                <td>${page.avgFirstPaint}ms</td>
                <td>${page.avgFirstContentfulPaint}ms</td>
                <td>${page.avgLCP}ms</td>
                <td>${page.avgCLS}</td>
            </tr>
        `).join('')}
    </table>
    
    <h2>API性能详情</h2>
    <table>
        <tr>
            <th>API</th>
            <th>响应时间</th>
            <th>成功率</th>
        </tr>
        ${report.apis.map(api => `
            <tr>
                <td>${api.name}</td>
                <td>${api.avgResponseTime}ms</td>
                <td>${api.successRate}%</td>
            </tr>
        `).join('')}
    </table>
</body>
</html>
  `;
}

// 主函数
async function main() {
  try {
    console.log(colors.blue('🚀 CryptoVault 性能测试开始...\n'));
    
    ensureOutputDir();
    
    // 测试页面性能
    const pages = [
      { url: CONFIG.baseUrl, name: '首页' },
      { url: `${CONFIG.baseUrl}/zh-CN/dashboard`, name: '仪表板' },
      { url: `${CONFIG.baseUrl}/zh-CN/wallet`, name: '钱包页面' },
    ];
    
    const pageResults = [];
    for (const page of pages) {
      const result = await testPagePerformance(page.url, page.name);
      pageResults.push(result);
    }
    
    // 测试API性能
    const apiResults = await testApiPerformance();
    
    // 分析资源大小
    const resourceAnalysis = await analyzeResourceSizes();
    
    // 生成报告
    const { jsonPath, htmlPath, report } = generateReport(pageResults, apiResults, resourceAnalysis);
    
    // 输出结果
    console.log(colors.green('\n✅ 性能测试完成！'));
    console.log(colors.yellow('\n📊 测试结果:'));
    console.log(`平均页面加载时间: ${report.summary.avgPageLoadTime}ms`);
    console.log(`平均API响应时间: ${report.summary.avgApiResponseTime}ms`);
    console.log(`总资源大小: ${report.summary.totalResourceSize}KB`);
    console.log(`总请求数: ${report.summary.totalRequests}`);
    
    console.log(colors.blue('\n📄 报告文件:'));
    console.log(`JSON报告: ${jsonPath}`);
    console.log(`HTML报告: ${htmlPath}`);
    
    if (report.recommendations.length > 0) {
      console.log(colors.yellow('\n💡 优化建议:'));
      report.recommendations.forEach(rec => {
        console.log(`${rec.category}: ${rec.message}`);
      });
    }
    
  } catch (error) {
    log.error(`测试失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { main };
