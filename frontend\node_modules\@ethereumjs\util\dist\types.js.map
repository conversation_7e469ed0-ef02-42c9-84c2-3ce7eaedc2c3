{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": ";;;AAAA,mCAA+D;AAC/D,yCAAwC;AAmDxC;;GAEG;AACH,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,+CAAM,CAAA;IACN,+CAAM,CAAA;IACN,+CAAM,CAAA;IACN,qEAAiB,CAAA;AACnB,CAAC,EALW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAKrB;AAqBD,SAAgB,MAAM,CACpB,KAAyB,EACzB,UAAa;IAEb,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,IAAI,CAAA;KACZ;IACD,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,SAAS,CAAA;KACjB;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAA,sBAAW,EAAC,KAAK,CAAC,EAAE;QACpD,MAAM,IAAI,KAAK,CAAC,sDAAsD,KAAK,EAAE,CAAC,CAAA;KAC/E;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QACpE,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAA;KACF;IAED,MAAM,MAAM,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAA;IAE9B,QAAQ,UAAU,EAAE;QAClB,KAAK,UAAU,CAAC,MAAM;YACpB,OAAO,MAAiC,CAAA;QAC1C,KAAK,UAAU,CAAC,MAAM;YACpB,OAAO,IAAA,sBAAc,EAAC,MAAM,CAA4B,CAAA;QAC1D,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC;YACtB,MAAM,MAAM,GAAG,IAAA,sBAAc,EAAC,MAAM,CAAC,CAAA;YACrC,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE;gBAC5C,MAAM,IAAI,KAAK,CACb,8FAA8F,CAC/F,CAAA;aACF;YACD,OAAO,MAAM,CAAC,MAAM,CAA4B,CAAA;SACjD;QACD,KAAK,UAAU,CAAC,iBAAiB;YAC/B,OAAO,IAAA,mBAAW,EAAC,MAAM,CAA4B,CAAA;QACvD;YACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;KACxC;AACH,CAAC;AAxCD,wBAwCC"}