{"version": 3, "file": "baseTransaction.d.ts", "sourceRoot": "", "sources": ["../src/baseTransaction.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,oBAAoB,CAAA;AAC5D,OAAO,EACL,OAAO,EAUR,MAAM,kBAAkB,CAAA;AAEzB,OAAO,EAAE,UAAU,EAAE,MAAM,SAAS,CAAA;AAGpC,OAAO,KAAK,EACV,uBAAuB,EACvB,4BAA4B,EAC5B,sBAAsB,EACtB,2BAA2B,EAC3B,MAAM,EACN,MAAM,EACN,SAAS,EACT,aAAa,EACd,MAAM,SAAS,CAAA;AAChB,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAA;AAElD,UAAU,gBAAgB;IACxB,IAAI,EAAE,MAAM,GAAG,SAAS,CAAA;IACxB,OAAO,CAAC,EAAE;QACR,KAAK,EAAE,MAAM,CAAA;QACb,QAAQ,EAAE,MAAM,GAAG,QAAQ,CAAA;KAC5B,CAAA;CACF;AAED;;;;;;GAMG;AACH,8BAAsB,eAAe,CAAC,iBAAiB;IACrD,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAQ;IAE9B,SAAgB,KAAK,EAAE,MAAM,CAAA;IAC7B,SAAgB,QAAQ,EAAE,MAAM,CAAA;IAChC,SAAgB,EAAE,CAAC,EAAE,OAAO,CAAA;IAC5B,SAAgB,KAAK,EAAE,MAAM,CAAA;IAC7B,SAAgB,IAAI,EAAE,MAAM,CAAA;IAE5B,SAAgB,CAAC,CAAC,EAAE,MAAM,CAAA;IAC1B,SAAgB,CAAC,CAAC,EAAE,MAAM,CAAA;IAC1B,SAAgB,CAAC,CAAC,EAAE,MAAM,CAAA;IAE1B,SAAgB,MAAM,EAAG,MAAM,CAAA;IAE/B,SAAS,CAAC,KAAK,EAAE,gBAAgB,CAGhC;IAED,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAA;IAEvC;;;;OAIG;IACH,SAAS,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAK;IAE3C;;;;;;;OAOG;IACH,SAAS,CAAC,aAAa,QAAgB;IAEvC;;;;;OAKG;IACH,SAAS,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,CAAiB;gBAElD,MAAM,EAAE,MAAM,GAAG,uBAAuB,GAAG,sBAAsB,EAAE,IAAI,EAAE,SAAS;IAqC9F;;;;OAIG;IACH,IAAI,IAAI,WAEP;IAED;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,CAAC,UAAU,EAAE,UAAU;IAI/B;;;OAGG;IACH,QAAQ,IAAI,OAAO;IACnB,QAAQ,CAAC,WAAW,EAAE,KAAK,GAAG,OAAO;IACrC,QAAQ,CAAC,WAAW,EAAE,IAAI,GAAG,MAAM,EAAE;IAerC,SAAS,CAAC,gBAAgB;IAQ1B;;;OAGG;IACH,SAAS,CAAC,cAAc;IAUxB;;OAEG;IACH,UAAU,IAAI,MAAM;IAWpB;;OAEG;IACH,UAAU,IAAI,MAAM;IAkBpB;;OAEG;IACH,QAAQ,CAAC,cAAc,IAAI,MAAM;IAEjC;;OAEG;IACH,iBAAiB,IAAI,OAAO;IAI5B;;;;;;;;;OASG;IACH,QAAQ,CAAC,GAAG,IAAI,aAAa,GAAG,4BAA4B,GAAG,2BAA2B;IAE1F;;OAEG;IACH,QAAQ,CAAC,SAAS,IAAI,MAAM;IAM5B,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,EAAE;IAChE,QAAQ,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,IAAI,GAAG,MAAM;IAErD,QAAQ,CAAC,IAAI,IAAI,MAAM;IAEvB,QAAQ,CAAC,2BAA2B,IAAI,MAAM;IAEvC,QAAQ,IAAI,OAAO;IAS1B;;OAEG;IACH,eAAe,IAAI,OAAO;IAU1B;;OAEG;IACH,gBAAgB,IAAI,OAAO;IAI3B;;OAEG;IACH,QAAQ,CAAC,kBAAkB,IAAI,MAAM;IAErC;;;;;;;;OAQG;IACH,IAAI,CAAC,UAAU,EAAE,MAAM,GAAG,iBAAiB;IAmC3C;;OAEG;IACH,QAAQ,CAAC,MAAM,IAAI,MAAM;IAGzB,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,iBAAiB;IAExF;;;;;;;OAOG;IACH,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,UAAU;IAuC1D;;;;;OAKG;IACH,SAAS,CAAC,+BAA+B,CACvC,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;KAAE,EAC7C,IAAI,SAAM,EACV,WAAW,UAAQ;IA4CrB,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE;IAyBjE;;OAEG;aACa,QAAQ,IAAI,MAAM;IAElC;;;;;OAKG;IACH,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAEjD;;;OAGG;IACH,SAAS,CAAC,sBAAsB;CAyBjC"}