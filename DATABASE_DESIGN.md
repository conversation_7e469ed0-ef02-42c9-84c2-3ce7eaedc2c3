# CryptoVault 数据库设计文档

## 设计理念

CryptoVault数据库设计遵循Web3去中心化原则，以钱包地址为核心身份标识，避免传统的KYC流程，同时保持高性能和可扩展性。

## 技术选择

### 数据库类型
- **开发环境**: SQLite - 轻量级，无需额外配置
- **生产环境**: PostgreSQL - 高性能，支持复杂查询
- **ORM**: Prisma - 类型安全，自动生成客户端

### 设计原则
1. **去中心化身份**: 以钱包地址为主键，无需传统注册
2. **多链支持**: 支持Ethereum、BSC、Polygon等多条链
3. **性能优化**: 针对弱服务器优化的索引和查询
4. **数据完整性**: 严格的外键约束和数据验证
5. **可扩展性**: 模块化设计，便于添加新功能

## 数据表结构

### 1. 用户表 (users)
**用途**: 存储基于钱包地址的用户信息

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| address | String | 钱包地址 | UNIQUE |
| ensName | String? | ENS域名 | - |
| avatar | String? | 头像IPFS链接 | - |
| theme | String | 主题偏好 | - |
| language | String | 语言偏好 | - |
| notifications | Boolean | 通知设置 | - |
| firstConnected | DateTime | 首次连接时间 | - |
| lastActive | DateTime | 最后活跃时间 | INDEX |

**特点**:
- 钱包地址作为唯一标识，支持ENS域名
- 用户偏好设置支持主题和多语言
- 无需传统的用户名/密码认证

### 2. 代币表 (tokens)
**用途**: 存储多链代币信息

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| address | String | 合约地址 | UNIQUE |
| chainId | Int | 链ID | INDEX |
| name | String | 代币名称 | - |
| symbol | String | 代币符号 | INDEX |
| decimals | Int | 精度 | - |
| totalSupply | String | 总供应量 | - |
| price | String | 当前价格 | - |
| marketCap | String | 市值 | - |
| volume24h | String | 24h交易量 | - |
| isVerified | Boolean | 验证状态 | INDEX |
| isActive | Boolean | 活跃状态 | INDEX |

**特点**:
- 支持多链代币 (chainId区分)
- 使用字符串存储大数值，避免精度丢失
- 包含市场数据和验证状态

### 3. 交易记录表 (transactions)
**用途**: 存储链上交易记录

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| hash | String | 交易哈希 | UNIQUE |
| fromAddress | String | 发送地址 | INDEX |
| toAddress | String | 接收地址 | INDEX |
| value | String | 交易金额 | - |
| gasUsed | String | Gas消耗 | - |
| gasPrice | String | Gas价格 | - |
| chainId | Int | 链ID | INDEX |
| blockNumber | BigInt | 区块号 | INDEX |
| type | Enum | 交易类型 | INDEX |
| status | Enum | 交易状态 | INDEX |
| timestamp | DateTime | 交易时间 | INDEX |

**特点**:
- 完整的交易信息记录
- 支持多种交易类型 (转账、质押、兑换等)
- 高效的查询索引

### 4. 质押池表 (staking_pools)
**用途**: DeFi质押池配置

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| name | String | 池名称 | - |
| stakingTokenId | String | 质押代币ID | FK |
| rewardTokenId | String | 奖励代币ID | FK |
| apy | Float | 年化收益率 | - |
| totalStaked | String | 总质押量 | - |
| totalRewards | String | 总奖励 | - |
| lockPeriod | Int | 锁定期(秒) | - |
| contractAddress | String | 合约地址 | UNIQUE |
| chainId | Int | 链ID | INDEX |
| isActive | Boolean | 活跃状态 | INDEX |

**特点**:
- 灵活的质押池配置
- 支持不同的质押和奖励代币
- 可配置的锁定期和最小质押量

### 5. 质押记录表 (staking_records)
**用途**: 用户质押记录

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| userId | String | 用户ID | FK, INDEX |
| poolId | String | 质押池ID | FK, INDEX |
| amount | String | 质押数量 | - |
| rewardDebt | String | 已计算奖励 | - |
| pendingRewards | String | 待领取奖励 | - |
| stakeTime | DateTime | 质押时间 | INDEX |
| unlockTime | DateTime? | 解锁时间 | INDEX |
| isActive | Boolean | 活跃状态 | INDEX |

**特点**:
- 详细的质押记录跟踪
- 奖励计算和分发管理
- 支持锁定期质押

### 6. NFT集合表 (nft_collections)
**用途**: NFT集合信息

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| address | String | 合约地址 | UNIQUE |
| chainId | Int | 链ID | INDEX |
| name | String | 集合名称 | - |
| symbol | String | 集合符号 | - |
| description | String? | 描述 | - |
| totalSupply | Int | 总供应量 | - |
| floorPrice | String | 地板价 | - |
| volume24h | String | 24h交易量 | - |
| isVerified | Boolean | 验证状态 | INDEX |

**特点**:
- 完整的NFT集合元数据
- 市场数据跟踪
- 验证状态管理

### 7. NFT表 (nfts)
**用途**: 单个NFT信息

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| tokenId | String | NFT Token ID | INDEX |
| collectionId | String | 集合ID | FK, INDEX |
| name | String? | NFT名称 | - |
| description | String? | 描述 | - |
| imageUrl | String? | 图片URL | - |
| attributes | Json? | 属性JSON | - |
| rarityRank | Int? | 稀有度排名 | INDEX |
| rarityScore | Float? | 稀有度分数 | INDEX |

**特点**:
- 灵活的NFT元数据存储
- 稀有度计算和排名
- JSON属性存储

### 8. NFT持有记录表 (nft_holdings)
**用途**: 用户NFT持有记录

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| userId | String | 用户ID | FK, INDEX |
| nftId | String | NFT ID | FK, INDEX |
| quantity | Int | 持有数量 | - |
| acquiredAt | DateTime | 获得时间 | INDEX |
| acquiredPrice | String? | 获得价格 | - |
| acquiredTxHash | String? | 获得交易哈希 | - |

**特点**:
- 支持ERC1155多数量持有
- 获得历史记录
- 价格跟踪

### 9. 空投活动表 (airdrop_campaigns)
**用途**: 空投活动管理

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| name | String | 活动名称 | - |
| tokenAddress | String | 代币地址 | INDEX |
| chainId | Int | 链ID | INDEX |
| totalAmount | String | 总空投量 | - |
| startTime | DateTime | 开始时间 | INDEX |
| endTime | DateTime | 结束时间 | INDEX |
| contractAddress | String? | 空投合约地址 | - |
| merkleRoot | String? | Merkle树根 | - |
| eligibilityRules | Json | 资格规则 | - |
| isActive | Boolean | 活跃状态 | INDEX |

**特点**:
- 灵活的空投活动配置
- Merkle树证明支持
- 复杂的资格规则

### 10. 空投领取记录表 (airdrop_claims)
**用途**: 空投领取记录

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| userId | String | 用户ID | FK, INDEX |
| campaignId | String | 活动ID | FK, INDEX |
| amount | String | 领取数量 | - |
| merkleProof | Json? | Merkle证明 | - |
| txHash | String? | 领取交易哈希 | - |
| status | Enum | 领取状态 | INDEX |
| claimedAt | DateTime? | 领取时间 | INDEX |

**特点**:
- 防重复领取机制
- Merkle证明验证
- 状态跟踪

### 11. 系统配置表 (system_configs)
**用途**: 系统配置管理

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | String | 主键 (CUID) | PK |
| key | String | 配置键 | UNIQUE |
| value | String | 配置值 | - |
| description | String? | 描述 | - |

**特点**:
- 灵活的系统配置
- 键值对存储
- 运行时配置更新

## 枚举定义

### TransactionType (交易类型)
- `TRANSFER`: 转账
- `SWAP`: 兑换
- `STAKE`: 质押
- `UNSTAKE`: 解质押
- `CLAIM`: 领取
- `MINT`: 铸造
- `BURN`: 销毁
- `APPROVE`: 授权

### TransactionStatus (交易状态)
- `PENDING`: 待确认
- `CONFIRMED`: 已确认
- `FAILED`: 失败
- `CANCELLED`: 已取消

### ClaimStatus (领取状态)
- `ELIGIBLE`: 符合条件
- `CLAIMED`: 已领取
- `EXPIRED`: 已过期
- `INELIGIBLE`: 不符合条件

## 性能优化

### 索引策略
1. **主键索引**: 所有表的id字段
2. **唯一索引**: address, hash等唯一字段
3. **查询索引**: 常用查询字段 (chainId, userId, timestamp等)
4. **复合索引**: 多字段组合查询

### 查询优化
1. **分页查询**: 使用cursor-based分页
2. **预加载**: 关联数据的eager loading
3. **缓存策略**: 热点数据Redis缓存
4. **读写分离**: 读多写少的场景优化

### 数据归档
1. **历史数据**: 定期归档旧交易记录
2. **日志清理**: 定期清理系统日志
3. **备份策略**: 定期数据库备份

## 安全考虑

### 数据保护
1. **敏感数据**: 不存储私钥等敏感信息
2. **数据验证**: 严格的输入验证
3. **SQL注入**: 使用Prisma ORM防护
4. **访问控制**: 基于角色的数据访问

### 隐私保护
1. **最小化原则**: 只存储必要数据
2. **匿名化**: 可选的数据匿名化
3. **GDPR合规**: 支持数据删除请求

## 部署和维护

### 数据库初始化
```bash
# 生成Prisma客户端
npm run db:generate

# 推送数据库模式
npm run db:push

# 运行种子数据
npm run db:seed

# 一键初始化
npm run db:init
```

### 数据库管理
```bash
# 查看数据库
npm run db:studio

# 重置数据库
npm run db:reset

# 数据库迁移
npm run db:migrate
```

### 监控和维护
1. **性能监控**: 查询性能跟踪
2. **容量监控**: 数据库大小监控
3. **备份验证**: 定期备份测试
4. **版本升级**: 平滑的数据库升级

---

*数据库设计文档版本: v1.0*
*创建时间: 2024-12-19*
*最后更新: 2024-12-19*
