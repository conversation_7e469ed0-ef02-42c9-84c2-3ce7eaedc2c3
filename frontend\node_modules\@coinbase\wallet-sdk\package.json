{"name": "@coinbase/wallet-sdk", "version": "3.9.3", "description": "Coinbase Wallet JavaScript SDK", "keywords": ["cipher", "cipherbrowser", "coinbase", "coinbasewallet", "eth", "ether", "ethereum", "etherium", "injection", "toshi", "wallet", "walletlink", "web3"], "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "repository": "https://github.com/coinbase/coinbase-wallet-sdk.git", "author": "Coinbase, Inc.", "license": "Apache-2.0", "scripts": {"pretest": "node compile-assets.js", "test": "jest", "test:coverage": "yarn test:unit && open coverage/lcov-report/index.html", "prebuild": "rm -rf ./build && node -p \"'export const LIB_VERSION = \\'' + require('./package.json').version + '\\';'\" > src/version.ts", "build": "node compile-assets.js && tsc -p ./tsconfig.build.json && cp -a src/vendor-js dist", "dev": "node compile-assets.js && tsc --watch", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx --fix"}, "dependencies": {"bn.js": "^5.2.1", "buffer": "^6.0.3", "clsx": "^1.2.1", "eth-block-tracker": "^7.1.0", "eth-json-rpc-filters": "^6.0.0", "eventemitter3": "^5.0.1", "keccak": "^3.0.3", "preact": "^10.16.0", "sha.js": "^2.4.11"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/plugin-proposal-decorators": "^7.22.7", "@babel/plugin-transform-react-jsx": "^7.22.5", "@babel/preset-env": "^7.22.9", "@babel/preset-typescript": "^7.22.5", "@metamask/json-rpc-engine": "^7.0.0", "@peculiar/webcrypto": "^1.4.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/preact": "^2.0.1", "@types/bn.js": "^4.11.6", "@types/jest": "^27.5.2", "@types/node": "^14.18.54", "@types/sha.js": "^2.4.1", "babel-jest": "^27.5.1", "jest": "^27.5.1", "jest-chrome": "^0.7.2", "jest-websocket-mock": "^2.4.0", "prettier": "^2.8.8", "sass": "^1.64.1", "ts-jest": "^27.1.5", "ts-node": "^10.9.1", "tslib": "^2.6.0", "typescript": "^5.1.6"}}