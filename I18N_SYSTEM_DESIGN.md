# CryptoVault 多语言系统设计

## 系统概述

设计一个完整的多语言国际化系统，支持中英日韩四种语言，由管理员在后台控制语言包，确保Web3 DApp能够服务全球用户。

## 技术架构

### 核心技术栈
- **next-intl**: Next.js官方推荐的国际化库
- **ICU MessageFormat**: 支持复数、性别等复杂语法
- **动态路由**: 基于locale的路由系统
- **服务端渲染**: SEO友好的多语言支持

### 支持语言
- **中文 (zh-CN)**: 简体中文 - 默认语言
- **英文 (en-US)**: 美式英语
- **日文 (ja-JP)**: 日本语
- **韩文 (ko-KR)**: 한국어

## 路由结构

### URL结构设计
```
/                    # 默认重定向到 /zh-CN
/zh-CN              # 中文版本
/en-US              # 英文版本
/ja-JP              # 日文版本
/ko-KR              # 韩文版本

/zh-CN/dashboard    # 中文仪表板
/en-US/dashboard    # 英文仪表板
/ja-JP/dashboard    # 日文仪表板
/ko-KR/dashboard    # 韩文仪表板
```

### Next.js App Router配置
```typescript
// app/[locale]/layout.tsx
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';

export default async function LocaleLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

## 语言文件结构

### 文件组织
```
messages/
├── zh-CN.json      # 中文语言包
├── en-US.json      # 英文语言包
├── ja-JP.json      # 日文语言包
└── ko-KR.json      # 韩文语言包
```

### 语言包结构
```json
{
  "common": {
    "loading": "加载中...",
    "error": "错误",
    "success": "成功",
    "cancel": "取消",
    "confirm": "确认",
    "save": "保存",
    "edit": "编辑",
    "delete": "删除"
  },
  "navigation": {
    "home": "首页",
    "dashboard": "仪表板",
    "wallet": "钱包",
    "nft": "NFT",
    "staking": "质押",
    "airdrop": "空投",
    "governance": "治理"
  },
  "wallet": {
    "connect": "连接钱包",
    "disconnect": "断开连接",
    "balance": "余额",
    "address": "地址",
    "network": "网络",
    "switchNetwork": "切换网络"
  },
  "token": {
    "name": "CryptoVault Token",
    "symbol": "CVT",
    "price": "价格",
    "marketCap": "市值",
    "volume": "交易量",
    "holders": "持有者数量"
  },
  "staking": {
    "stake": "质押",
    "unstake": "解除质押",
    "rewards": "奖励",
    "apy": "年化收益率",
    "totalStaked": "总质押量",
    "yourStake": "您的质押"
  },
  "nft": {
    "collection": "收藏",
    "marketplace": "市场",
    "mint": "铸造",
    "transfer": "转移",
    "metadata": "元数据",
    "rarity": "稀有度"
  },
  "airdrop": {
    "claim": "领取",
    "eligible": "符合条件",
    "amount": "数量",
    "deadline": "截止时间",
    "tasks": "任务",
    "completed": "已完成",
    "verify": "验证",
    "requirements": "要求",
    "whitelist": "白名单",
    "merkleProof": "Merkle证明"
  },
  "forms": {
    "validation": {
      "required": "此字段为必填项",
      "email": "请输入有效的邮箱地址",
      "minLength": "最少需要 {min} 个字符",
      "maxLength": "最多允许 {max} 个字符"
    }
  },
  "errors": {
    "networkError": "网络连接错误",
    "walletNotConnected": "请先连接钱包",
    "insufficientBalance": "余额不足",
    "transactionFailed": "交易失败",
    "unknownError": "未知错误"
  }
}
```

## 复杂语法支持

### ICU MessageFormat示例
```json
{
  "messages": {
    "itemCount": "{count, plural, =0 {没有物品} =1 {1个物品} other {#个物品}}",
    "timeAgo": "{minutes, plural, =0 {刚刚} =1 {1分钟前} other {#分钟前}}",
    "balance": "您的余额: {amount, number, currency}",
    "welcome": "欢迎, {name}!"
  }
}
```

### 使用示例
```typescript
import { useTranslations } from 'next-intl';

function ItemList({ items }: { items: any[] }) {
  const t = useTranslations('messages');
  
  return (
    <div>
      <h2>{t('itemCount', { count: items.length })}</h2>
      {/* 输出: "3个物品" 或 "没有物品" */}
    </div>
  );
}
```

## 语言切换功能

### 语言选择器组件
```typescript
// components/LanguageSelector.tsx
import { useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';

const languages = [
  { code: 'zh-CN', name: '中文', flag: '🇨🇳' },
  { code: 'en-US', name: 'English', flag: '🇺🇸' },
  { code: 'ja-JP', name: '日本語', flag: '🇯🇵' },
  { code: 'ko-KR', name: '한국어', flag: '🇰🇷' },
];

export function LanguageSelector() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = (newLocale: string) => {
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
  };

  return (
    <select 
      value={locale} 
      onChange={(e) => handleLanguageChange(e.target.value)}
      className="bg-surface text-text-primary border border-border rounded-theme-md"
    >
      {languages.map((lang) => (
        <option key={lang.code} value={lang.code}>
          {lang.flag} {lang.name}
        </option>
      ))}
    </select>
  );
}
```

## 管理员语言包管理

### 后台管理API
```typescript
// backend/routes/admin/i18n.ts
import express from 'express';
const router = express.Router();

// 获取所有语言包
router.get('/locales', async (req, res) => {
  try {
    const locales = await getAvailableLocales();
    res.json(locales);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch locales' });
  }
});

// 获取特定语言的翻译
router.get('/locales/:locale', async (req, res) => {
  try {
    const { locale } = req.params;
    const translations = await getTranslations(locale);
    res.json(translations);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch translations' });
  }
});

// 更新翻译
router.put('/locales/:locale', async (req, res) => {
  try {
    const { locale } = req.params;
    const { translations } = req.body;
    await updateTranslations(locale, translations);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: 'Failed to update translations' });
  }
});

// 添加新的翻译键
router.post('/locales/:locale/keys', async (req, res) => {
  try {
    const { locale } = req.params;
    const { key, value } = req.body;
    await addTranslationKey(locale, key, value);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: 'Failed to add translation key' });
  }
});

export default router;
```

### 管理界面功能
- **翻译编辑器**: 可视化编辑翻译内容
- **批量导入导出**: 支持CSV/JSON格式
- **翻译状态跟踪**: 显示翻译完成度
- **实时预览**: 修改后立即预览效果
- **版本控制**: 翻译历史记录和回滚

## 数据库设计

### 翻译表结构
```sql
-- 翻译表
CREATE TABLE translations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  locale VARCHAR(10) NOT NULL,
  namespace VARCHAR(50) NOT NULL,
  key VARCHAR(255) NOT NULL,
  value TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(locale, namespace, key)
);

-- 翻译历史表
CREATE TABLE translation_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  translation_id INTEGER NOT NULL,
  old_value TEXT,
  new_value TEXT NOT NULL,
  changed_by VARCHAR(255),
  changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (translation_id) REFERENCES translations(id)
);

-- 语言配置表
CREATE TABLE locale_config (
  locale VARCHAR(10) PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  native_name VARCHAR(50) NOT NULL,
  flag VARCHAR(10),
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  rtl BOOLEAN DEFAULT false
);
```

## 性能优化

### 静态生成优化
```typescript
// 生成静态路径
export async function generateStaticParams() {
  return [
    { locale: 'zh-CN' },
    { locale: 'en-US' },
    { locale: 'ja-JP' },
    { locale: 'ko-KR' },
  ];
}
```

### 语言包分割
- 按页面分割语言包
- 懒加载非关键翻译
- 缓存常用翻译

### 缓存策略
- 浏览器缓存语言包
- CDN缓存静态翻译文件
- 服务端缓存动态翻译

## 开发工具

### 翻译键提取工具
```bash
# 自动提取代码中的翻译键
npm run extract-i18n

# 检查缺失的翻译
npm run check-i18n

# 生成翻译报告
npm run i18n-report
```

### 开发辅助
- VSCode插件支持
- 翻译键自动补全
- 实时翻译预览
- 翻译覆盖率检查

## 测试策略

### 国际化测试
- 各语言页面渲染测试
- 翻译键完整性测试
- 语言切换功能测试
- RTL语言支持测试

### 自动化测试
```typescript
// 翻译测试示例
describe('Internationalization', () => {
  test('should render Chinese text correctly', () => {
    render(<HomePage locale="zh-CN" />);
    expect(screen.getByText('首页')).toBeInTheDocument();
  });

  test('should render English text correctly', () => {
    render(<HomePage locale="en-US" />);
    expect(screen.getByText('Home')).toBeInTheDocument();
  });
});
```

## 部署配置

### 环境变量
```env
# 默认语言
DEFAULT_LOCALE=zh-CN

# 支持的语言列表
SUPPORTED_LOCALES=zh-CN,en-US,ja-JP,ko-KR

# 翻译API配置
TRANSLATION_API_URL=https://api.example.com/translations
TRANSLATION_API_KEY=your-api-key
```

### 构建优化
- 按语言分包构建
- 静态文件预生成
- CDN部署优化

---

*设计文档版本: v1.0*
*创建时间: 2024-12-19*
*最后更新: 2024-12-19*
