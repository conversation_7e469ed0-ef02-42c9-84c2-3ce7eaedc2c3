/// <reference types="node" />
/**
 * Throws if a string is not hex prefixed
 * @param {string} input string to check hex prefix of
 */
export declare const assertIsHexString: (input: string) => void;
/**
 * Throws if input is not a buffer
 * @param {Buffer} input value to check
 */
export declare const assertIsBuffer: (input: Buffer) => void;
/**
 * Throws if input is not an array
 * @param {number[]} input value to check
 */
export declare const assertIsArray: (input: number[]) => void;
/**
 * Throws if input is not a string
 * @param {string} input value to check
 */
export declare const assertIsString: (input: string) => void;
//# sourceMappingURL=helpers.d.ts.map