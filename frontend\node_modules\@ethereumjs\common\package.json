{"name": "@ethereumjs/common", "version": "3.2.0", "description": "Resources common to all Ethereum implementations", "keywords": ["ethereum", "ethereumjs", "constants", "parameters", "genesis", "networks", "bootstrap"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/common#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+common%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "../../config/cli/ts-build.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "../../config/cli/coverage.sh", "docs:build": "typedoc --options typedoc.js", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "prepublishOnly": "../../config/cli/prepublish.sh", "tape": "tape -r ts-node/register", "test": "npm run test:node && npm run test:browser", "test:browser": "karma start karma.conf.js", "test:node": "npm run tape -- ./test/*.spec.ts", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@ethereumjs/util": "^8.1.0", "crc-32": "^1.2.0"}}