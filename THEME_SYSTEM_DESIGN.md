# CryptoVault 主题系统设计

## 系统概述

设计一个灵活、可扩展的主题系统，支持多套UI风格动态切换，方便开发其他类似项目时快速定制外观。

## 主题架构

### 核心设计原则
1. **配置驱动**: 通过配置文件定义主题
2. **CSS变量**: 基于CSS自定义属性实现
3. **组件解耦**: 组件与主题分离
4. **动态切换**: 运行时无刷新切换主题
5. **可扩展性**: 易于添加新主题

### 技术实现
- **CSS变量**: 定义主题色彩和尺寸
- **Tailwind CSS**: 扩展配置支持主题
- **React Context**: 主题状态管理
- **localStorage**: 主题偏好持久化

## 预设主题

### 1. Electric Minimal (默认主题)
```css
:root[data-theme="electric-minimal"] {
  /* 背景色 */
  --color-background: #000000;
  --color-surface: #1a1a1a;
  --color-surface-hover: #2a2a2a;
  
  /* 主要色彩 */
  --color-primary: #00d4ff;
  --color-primary-hover: #00b8e6;
  --color-primary-light: rgba(0, 212, 255, 0.1);
  
  /* 文字色彩 */
  --color-text-primary: #ffffff;
  --color-text-secondary: #a0a0a0;
  --color-text-muted: #666666;
  
  /* 边框和分割线 */
  --color-border: #333333;
  --color-border-light: #444444;
  
  /* 状态色彩 */
  --color-success: #00ff88;
  --color-warning: #ffaa00;
  --color-error: #ff4444;
  --color-info: #00d4ff;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 212, 255, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 212, 255, 0.15);
  --shadow-lg: 0 10px 15px rgba(0, 212, 255, 0.2);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}
```

### 2. Ocean Breeze (备选主题)
```css
:root[data-theme="ocean-breeze"] {
  --color-background: #0a1628;
  --color-surface: #1e3a5f;
  --color-primary: #00bcd4;
  --color-text-primary: #ffffff;
  --color-text-secondary: #b0bec5;
  /* ... 其他变量 */
}
```

### 3. Forest Zen (备选主题)
```css
:root[data-theme="forest-zen"] {
  --color-background: #1a2e1a;
  --color-surface: #2d4a2d;
  --color-primary: #4caf50;
  --color-text-primary: #ffffff;
  --color-text-secondary: #c8e6c9;
  /* ... 其他变量 */
}
```

## 主题配置文件

### 主题定义结构
```typescript
interface ThemeConfig {
  id: string;
  name: string;
  description: string;
  colors: {
    background: string;
    surface: string;
    primary: string;
    text: {
      primary: string;
      secondary: string;
      muted: string;
    };
    border: string;
    status: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
  radius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  animations: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      ease: string;
      easeIn: string;
      easeOut: string;
    };
  };
}
```

### 主题注册文件
```typescript
// lib/themes/registry.ts
export const themeRegistry: ThemeConfig[] = [
  {
    id: 'electric-minimal',
    name: 'Electric Minimal',
    description: '极简电光风格，深色主题',
    colors: { /* ... */ },
    // ... 其他配置
  },
  {
    id: 'ocean-breeze',
    name: 'Ocean Breeze',
    description: '海洋微风风格，蓝色主题',
    colors: { /* ... */ },
    // ... 其他配置
  }
];
```

## React主题系统

### 主题Context
```typescript
// lib/contexts/ThemeContext.tsx
interface ThemeContextType {
  currentTheme: string;
  themes: ThemeConfig[];
  setTheme: (themeId: string) => void;
  getThemeConfig: (themeId: string) => ThemeConfig | undefined;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [currentTheme, setCurrentTheme] = useState('electric-minimal');
  
  const setTheme = useCallback((themeId: string) => {
    setCurrentTheme(themeId);
    document.documentElement.setAttribute('data-theme', themeId);
    localStorage.setItem('preferred-theme', themeId);
  }, []);
  
  // ... 其他逻辑
  
  return (
    <ThemeContext.Provider value={{ currentTheme, themes, setTheme, getThemeConfig }}>
      {children}
    </ThemeContext.Provider>
  );
}
```

### 主题Hook
```typescript
// hooks/useTheme.ts
export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
```

## Tailwind CSS集成

### 扩展配置
```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        background: 'var(--color-background)',
        surface: 'var(--color-surface)',
        primary: 'var(--color-primary)',
        'text-primary': 'var(--color-text-primary)',
        'text-secondary': 'var(--color-text-secondary)',
        border: 'var(--color-border)',
        success: 'var(--color-success)',
        warning: 'var(--color-warning)',
        error: 'var(--color-error)',
      },
      boxShadow: {
        'theme-sm': 'var(--shadow-sm)',
        'theme-md': 'var(--shadow-md)',
        'theme-lg': 'var(--shadow-lg)',
      },
      borderRadius: {
        'theme-sm': 'var(--radius-sm)',
        'theme-md': 'var(--radius-md)',
        'theme-lg': 'var(--radius-lg)',
        'theme-xl': 'var(--radius-xl)',
      },
    },
  },
  plugins: [],
};
```

## 组件主题支持

### 主题化组件示例
```typescript
// components/ui/Button.tsx
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

export function Button({ variant = 'primary', size = 'md', children }: ButtonProps) {
  const baseClasses = 'font-medium rounded-theme-md transition-colors';
  
  const variantClasses = {
    primary: 'bg-primary text-background hover:bg-primary-hover',
    secondary: 'bg-surface text-text-primary hover:bg-surface-hover',
    outline: 'border border-border text-text-primary hover:bg-surface',
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };
  
  return (
    <button className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}>
      {children}
    </button>
  );
}
```

## 管理员主题控制

### 后台主题管理API
```typescript
// backend/routes/admin/themes.ts
router.get('/themes', async (req, res) => {
  // 获取所有可用主题
  const themes = await getAvailableThemes();
  res.json(themes);
});

router.post('/themes/active', async (req, res) => {
  // 设置活跃主题
  const { themeId } = req.body;
  await setActiveTheme(themeId);
  res.json({ success: true });
});

router.post('/themes/custom', async (req, res) => {
  // 创建自定义主题
  const themeConfig = req.body;
  await createCustomTheme(themeConfig);
  res.json({ success: true });
});
```

### 主题管理界面
- 主题预览功能
- 实时主题切换
- 自定义主题编辑器
- 主题导入导出

## 主题扩展指南

### 添加新主题步骤
1. 在 `lib/themes/` 目录创建主题配置文件
2. 定义CSS变量和样式
3. 注册到主题注册表
4. 添加主题预览图
5. 更新管理界面

### 自定义主题开发
```typescript
// 创建新主题配置
const customTheme: ThemeConfig = {
  id: 'custom-theme',
  name: 'Custom Theme',
  description: '自定义主题描述',
  colors: {
    // 定义颜色
  },
  // ... 其他配置
};

// 注册主题
registerTheme(customTheme);
```

## 性能优化

### CSS变量优化
- 减少CSS变量数量
- 合理组织变量层级
- 避免频繁的主题切换

### 加载优化
- 主题样式按需加载
- CSS文件分割
- 缓存主题配置

### 运行时优化
- 主题切换防抖
- 样式计算缓存
- 组件重渲染优化

---

*设计文档版本: v1.0*
*创建时间: 2024-12-19*
*最后更新: 2024-12-19*
