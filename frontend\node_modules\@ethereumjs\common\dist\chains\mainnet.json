{"name": "mainnet", "chainId": 1, "networkId": 1, "defaultHardfork": "merge", "consensus": {"type": "pow", "algorithm": "ethash", "ethash": {}}, "comment": "The Ethereum main chain", "url": "https://ethstats.net/", "genesis": {"gasLimit": 5000, "difficulty": 17179869184, "nonce": "0x0000000000000042", "extraData": "0x11bbe8db4e347b4e8c937c1c8370e4b5ed33adb3db69cbdb7a38e1e50b1b82fa"}, "hardforks": [{"name": "chainstart", "block": 0, "forkHash": "0xfc64ec04"}, {"name": "homestead", "block": 1150000, "forkHash": "0x97c2c34c"}, {"name": "dao", "block": 1920000, "forkHash": "0x91d1f948"}, {"name": "tangerineWhistle", "block": 2463000, "forkHash": "0x7a64da13"}, {"name": "spurious<PERSON><PERSON><PERSON>", "block": 2675000, "forkHash": "0x3edd5b10"}, {"name": "byzantium", "block": 4370000, "forkHash": "0xa00bc324"}, {"name": "constantinople", "block": 7280000, "forkHash": "0x668db0af"}, {"name": "petersburg", "block": 7280000, "forkHash": "0x668db0af"}, {"name": "istanbul", "block": 9069000, "forkHash": "0x879d6e30"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block": 9200000, "forkHash": "0xe029e991"}, {"name": "berlin", "block": 12244000, "forkHash": "0x0eb440f6"}, {"name": "london", "block": 12965000, "forkHash": "0xb715077d"}, {"name": "arrowGlacier", "block": 13773000, "forkHash": "0x20c327fc"}, {"name": "grayGla<PERSON>", "block": 15050000, "forkHash": "0xf0afd0e3"}, {"//_comment": "The forkHash will remain same as mergeForkIdTransition is post merge, terminal block: https://etherscan.io/block/15537393", "name": "merge", "ttd": "58750000000000000000000", "block": 15537394, "forkHash": "0xf0afd0e3"}, {"name": "mergeForkIdTransition", "block": null, "forkHash": null}, {"name": "shanghai", "block": null, "timestamp": "1681338455", "forkHash": "0xdce96c2d"}], "bootstrapNodes": [{"ip": "*************", "port": 30303, "id": "d860a01f9722d78051619d1e2351aba3f43f943f6f00718d1b9baa4101932a1f5011f16bb2b1bb35db20d6fe28fa0bf09636d26a87d31de9ec6203eeedb1f666", "location": "ap-southeast-1-001", "comment": "bootnode-aws-ap-southeast-1-001"}, {"ip": "***********", "port": 30303, "id": "22a8232c3abc76a16ae9d6c3b164f98775fe226f0917b0ca871128a74a8e9630b458460865bab457221f1d448dd9791d24c4e5d88786180ac185df813a68d4de", "location": "us-east-1-001", "comment": "bootnode-aws-us-east-1-001"}, {"ip": "*************", "port": 30303, "id": "2b252ab6a1d0f971d9722cb839a42cb81db019ba44c08754628ab4a823487071b5695317c8ccd085219c3a03af063495b2f1da8d18218da2d6a82981b45e6ffc", "location": "eu-west-1-001", "comment": "bootnode-hetzner-hel"}, {"ip": "*************", "port": 30303, "id": "4aeb4ab6c14b23e2c4cfdce879c04b0748a20d8e9b59e25ded2a08143e265c6c25936e74cbc8e641e3312ca288673d91f2f93f8e277de3cfa444ecdaaf982052", "location": "eu-central-1-001", "comment": "bootnode-hetzner-fsn"}], "dnsNetworks": ["enrtree://<EMAIL>"]}