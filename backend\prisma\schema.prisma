// CryptoVault 数据库模式设计
// 针对Web3 DApp的去中心化特性优化

generator client {
  provider = "prisma-client-js"
  engineType = "binary"
  binaryTargets = ["native"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表 - 基于钱包地址的去中心化身份
model User {
  id              String   @id @default(cuid())
  address         String   @unique // 钱包地址作为主要标识
  ensName         String?  // ENS域名
  avatar          String?  // 头像IPFS链接
  
  // 用户偏好设置
  theme           String   @default("electric-minimal")
  language        String   @default("zh-CN")
  notifications   Boolean  @default(true)
  
  // 时间戳
  firstConnected  DateTime @default(now())
  lastActive      DateTime @updatedAt
  
  // 关联关系
  transactions    Transaction[]
  stakingRecords  StakingRecord[]
  nftHoldings     NFTHolding[]
  airdrops        AirdropClaim[]
  
  @@map("users")
}

// 代币信息表
model Token {
  id              String   @id @default(cuid())
  address         String   @unique // 合约地址
  chainId         Int      // 链ID (1=Ethereum, 56=BSC, 137=Polygon)
  
  // 基本信息
  name            String
  symbol          String
  decimals        Int      @default(18)
  totalSupply     String   // 使用字符串存储大数
  
  // 市场数据
  price           String   @default("0")
  marketCap       String   @default("0")
  volume24h       String   @default("0")
  priceChange24h  String   @default("0")
  
  // 元数据
  logoUrl         String?
  description     String?
  website         String?
  whitepaper      String?
  
  // 验证状态
  isVerified      Boolean  @default(false)
  isActive        Boolean  @default(true)
  
  // 时间戳
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // 关联关系
  transactions    Transaction[]
  stakingPools    StakingPool[]
  
  @@map("tokens")
}

// 交易记录表
model Transaction {
  id              String   @id @default(cuid())
  hash            String   @unique // 交易哈希
  
  // 基本信息
  fromAddress     String
  toAddress       String
  value           String   // 交易金额
  gasUsed         String
  gasPrice        String
  
  // 关联信息
  userId          String?
  tokenId         String?
  chainId         Int
  blockNumber     BigInt
  blockHash       String
  
  // 交易类型和状态
  type            TransactionType
  status          TransactionStatus @default(PENDING)
  
  // 时间戳
  timestamp       DateTime
  createdAt       DateTime @default(now())
  
  // 关联关系
  user            User?    @relation(fields: [userId], references: [id])
  token           Token?   @relation(fields: [tokenId], references: [id])
  
  @@map("transactions")
}

// 质押池表
model StakingPool {
  id              String   @id @default(cuid())
  name            String
  description     String?
  
  // 代币信息
  stakingTokenId  String
  rewardTokenId   String
  
  // 池参数
  apy             Float    // 年化收益率
  totalStaked     String   @default("0")
  totalRewards    String   @default("0")
  lockPeriod      Int      @default(0) // 锁定期(秒)
  minStakeAmount  String   @default("0")
  maxStakeAmount  String?
  
  // 合约信息
  contractAddress String   @unique
  chainId         Int
  
  // 状态
  isActive        Boolean  @default(true)
  startTime       DateTime?
  endTime         DateTime?
  
  // 时间戳
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // 关联关系
  stakingToken    Token    @relation(fields: [stakingTokenId], references: [id])
  rewardToken     Token    @relation(fields: [rewardTokenId], references: [id])
  stakingRecords  StakingRecord[]
  
  @@map("staking_pools")
}

// 质押记录表
model StakingRecord {
  id              String   @id @default(cuid())
  
  // 用户和池信息
  userId          String
  poolId          String
  
  // 质押信息
  amount          String   // 质押数量
  rewardDebt      String   @default("0") // 已计算奖励
  pendingRewards  String   @default("0") // 待领取奖励
  
  // 时间信息
  stakeTime       DateTime @default(now())
  unlockTime      DateTime? // 解锁时间
  lastClaimTime   DateTime? // 最后领取时间
  
  // 状态
  isActive        Boolean  @default(true)
  
  // 关联关系
  user            User     @relation(fields: [userId], references: [id])
  pool            StakingPool @relation(fields: [poolId], references: [id])
  
  @@map("staking_records")
}

// NFT集合表
model NFTCollection {
  id              String   @id @default(cuid())
  address         String   @unique // 合约地址
  chainId         Int
  
  // 基本信息
  name            String
  symbol          String
  description     String?
  
  // 元数据
  imageUrl        String?
  bannerUrl       String?
  website         String?
  discord         String?
  twitter         String?
  
  // 统计信息
  totalSupply     Int      @default(0)
  floorPrice      String   @default("0")
  volume24h       String   @default("0")
  
  // 状态
  isVerified      Boolean  @default(false)
  isActive        Boolean  @default(true)
  
  // 时间戳
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // 关联关系
  nfts            NFT[]
  
  @@map("nft_collections")
}

// NFT表
model NFT {
  id              String   @id @default(cuid())
  tokenId         String   // NFT Token ID
  collectionId    String
  
  // 元数据
  name            String?
  description     String?
  imageUrl        String?
  animationUrl    String?
  externalUrl     String?
  attributes      Json?    // NFT属性JSON
  
  // 稀有度信息
  rarityRank      Int?
  rarityScore     Float?
  
  // 时间戳
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // 关联关系
  collection      NFTCollection @relation(fields: [collectionId], references: [id])
  holdings        NFTHolding[]
  
  @@unique([tokenId, collectionId])
  @@map("nfts")
}

// NFT持有记录表
model NFTHolding {
  id              String   @id @default(cuid())
  
  // 持有信息
  userId          String
  nftId           String
  quantity        Int      @default(1) // ERC1155支持
  
  // 获得信息
  acquiredAt      DateTime @default(now())
  acquiredPrice   String?
  acquiredTxHash  String?
  
  // 关联关系
  user            User     @relation(fields: [userId], references: [id])
  nft             NFT      @relation(fields: [nftId], references: [id])
  
  @@unique([userId, nftId])
  @@map("nft_holdings")
}

// 空投活动表
model AirdropCampaign {
  id              String   @id @default(cuid())
  name            String
  description     String?
  
  // 代币信息
  tokenAddress    String
  chainId         Int
  totalAmount     String   // 总空投数量
  
  // 时间信息
  startTime       DateTime
  endTime         DateTime
  claimDeadline   DateTime?
  
  // 合约信息
  contractAddress String?  // 空投合约地址
  merkleRoot      String?  // Merkle树根哈希
  
  // 条件设置
  eligibilityRules Json    // 资格规则JSON
  
  // 状态
  isActive        Boolean  @default(true)
  
  // 时间戳
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // 关联关系
  claims          AirdropClaim[]
  
  @@map("airdrop_campaigns")
}

// 空投领取记录表
model AirdropClaim {
  id              String   @id @default(cuid())
  
  // 用户和活动信息
  userId          String
  campaignId      String
  
  // 领取信息
  amount          String   // 领取数量
  merkleProof     Json?    // Merkle证明
  txHash          String?  // 领取交易哈希
  
  // 状态
  status          ClaimStatus @default(ELIGIBLE)
  
  // 时间戳
  claimedAt       DateTime?
  createdAt       DateTime @default(now())
  
  // 关联关系
  user            User     @relation(fields: [userId], references: [id])
  campaign        AirdropCampaign @relation(fields: [campaignId], references: [id])
  
  @@unique([userId, campaignId])
  @@map("airdrop_claims")
}

// 系统配置表
model SystemConfig {
  id              String   @id @default(cuid())
  key             String   @unique
  value           String
  description     String?
  
  // 时间戳
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@map("system_configs")
}

// 枚举定义
enum TransactionType {
  TRANSFER
  SWAP
  STAKE
  UNSTAKE
  CLAIM
  MINT
  BURN
  APPROVE
}

enum TransactionStatus {
  PENDING
  CONFIRMED
  FAILED
  CANCELLED
}

enum ClaimStatus {
  ELIGIBLE
  CLAIMED
  EXPIRED
  INELIGIBLE
}
