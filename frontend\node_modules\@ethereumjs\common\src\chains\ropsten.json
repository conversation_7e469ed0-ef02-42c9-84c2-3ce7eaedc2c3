{"name": "ropsten", "chainId": 3, "networkId": 3, "defaultHardfork": "merge", "consensus": {"type": "pow", "algorithm": "ethash", "ethash": {}}, "comment": "PoW test network", "url": "https://github.com/ethereum/ropsten", "genesis": {"gasLimit": 16777216, "difficulty": 1048576, "nonce": "0x0000000000000042", "extraData": "0x3535353535353535353535353535353535353535353535353535353535353535"}, "hardforks": [{"name": "chainstart", "block": 0, "forkHash": "0x30c7ddbc"}, {"name": "homestead", "block": 0, "forkHash": "0x30c7ddbc"}, {"name": "tangerineWhistle", "block": 0, "forkHash": "0x30c7ddbc"}, {"name": "spurious<PERSON><PERSON><PERSON>", "block": 10, "forkHash": "0x63760190"}, {"name": "byzantium", "block": 1700000, "forkHash": "0x3ea159c7"}, {"name": "constantinople", "block": 4230000, "forkHash": "0x97b544f3"}, {"name": "petersburg", "block": 4939394, "forkHash": "0xd6e2149b"}, {"name": "istanbul", "block": 6485846, "forkHash": "0x4bc66396"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block": 7117117, "forkHash": "0x6727ef90"}, {"name": "berlin", "block": 9812189, "forkHash": "0xa157d377"}, {"name": "london", "block": 10499401, "forkHash": "0x7119b6b3"}, {"//_comment": "The forkHash will remain same as mergeForkIdTransition is post merge", "name": "merge", "ttd": "50000000000000000", "block": null, "forkHash": "0x7119b6b3"}, {"name": "mergeForkIdTransition", "block": null, "forkHash": null}, {"name": "shanghai", "block": null, "forkHash": null}], "bootstrapNodes": [{"ip": "***********", "port": 30303, "id": "30b7ab30a01c124a6cceca36863ece12c4f5fa68e3ba9b0b51407ccc002eeed3b3102d20a88f1c1d3c3154e2449317b8ef95090e77b312d5cc39354f86d5d606", "location": "", "comment": "US-Azure geth"}, {"ip": "*************", "port": 30303, "id": "865a63255b3bb68023b6bffd5095118fcc13e79dcf014fe4e47e065c350c7cc72af2e53eff895f11ba1bbb6a2b33271c1116ee870f266618eadfc2e78aa7349c", "location": "", "comment": "US-Azure parity"}, {"ip": "**************", "port": 30303, "id": "6332792c4a00e3e4ee0926ed89e0d27ef985424d97b6a45bf0f23e51f0dcb5e66b875777506458aea7af6f9e4ffb69f43f3778ee73c81ed9d34c51c4b16b0b0f", "location": "", "comment": "Parity"}, {"ip": "**************", "port": 30303, "id": "94c15d1b9e2fe7ce56e458b9a3b672ef11894ddedd0c6f247e0f1d3487f52b66208fb4aeb8179fce6e3a749ea93ed147c37976d67af557508d199d9594c35f09", "location": "", "comment": "@gpip"}], "dnsNetworks": ["enrtree://<EMAIL>"]}