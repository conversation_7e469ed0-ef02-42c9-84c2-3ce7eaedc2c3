# CryptoVault 性能优化方案

## 服务器配置分析

**目标服务器**: Intel(R) Xeon(R) CPU E5-2699 v4 @ 2.20GHz 2 Virtual Core

### 硬件限制分析
- **CPU**: 2核心，相对较低的主频
- **内存**: 未知，假设2-4GB
- **存储**: 未知，假设SSD
- **网络**: 未知带宽

### 优化目标
- **首屏加载时间**: < 2秒
- **页面切换**: < 500ms
- **API响应时间**: < 200ms
- **并发用户**: 支持100+用户同时在线
- **内存使用**: < 1GB
- **CPU使用**: < 80%

## 前端性能优化

### 1. 构建优化
```javascript
// next.config.js - 生产环境优化配置
const nextConfig = {
  // 启用SWC编译器 (比Babel快20倍)
  swcMinify: true,
  
  // 启用压缩
  compress: true,
  
  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 31536000, // 1年缓存
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  
  // 实验性功能
  experimental: {
    // 启用App Router
    appDir: true,
    // 减少JavaScript包大小
    optimizeCss: true,
    // 启用Turbo
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  
  // Webpack优化
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // 生产环境优化
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
          },
        },
      };
    }
    return config;
  },
  
  // 输出优化
  output: 'standalone',
  
  // 压缩配置
  compress: true,
  poweredByHeader: false,
  
  // 静态资源优化
  assetPrefix: process.env.NODE_ENV === 'production' ? '/static' : '',
};
```

### 2. 代码分割策略
```typescript
// 路由级别的代码分割
const Dashboard = dynamic(() => import('./Dashboard'), {
  loading: () => <DashboardSkeleton />,
  ssr: false, // 客户端渲染减少服务器负载
});

const WalletPage = dynamic(() => import('./WalletPage'), {
  loading: () => <PageSkeleton />,
});

// 组件级别的懒加载
const HeavyChart = dynamic(() => import('./HeavyChart'), {
  loading: () => <ChartSkeleton />,
  ssr: false,
});

// 条件加载
const AdminPanel = dynamic(() => import('./AdminPanel'), {
  loading: () => <div>Loading admin...</div>,
  ssr: false,
});
```

### 3. 资源优化
```typescript
// 图片优化组件
import Image from 'next/image';

function OptimizedImage({ src, alt, ...props }) {
  return (
    <Image
      src={src}
      alt={alt}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
      quality={75} // 降低质量减少文件大小
      priority={false} // 非关键图片延迟加载
      {...props}
    />
  );
}

// 字体优化
import { Inter } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap', // 字体交换策略
  preload: true,
  variable: '--font-inter',
});
```

### 4. 缓存策略
```typescript
// Service Worker缓存
// public/sw.js
const CACHE_NAME = 'cryptovault-v1';
const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js',
  '/images/logo.png',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

// 浏览器缓存配置
const cacheConfig = {
  'Cache-Control': 'public, max-age=31536000, immutable', // 静态资源1年缓存
  'ETag': true,
  'Last-Modified': true,
};
```

## 后端性能优化

### 1. 数据库优化
```typescript
// Prisma配置优化
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
  previewFeatures = ["jsonProtocol"] // 减少传输大小
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 连接池配置
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error'],
});

// 查询优化
async function getOptimizedUserData(userId: string) {
  return await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      address: true,
      preferences: true,
      // 只选择需要的字段
    },
  });
}
```

### 2. API缓存策略
```typescript
// 内存缓存实现
class MemoryCache {
  private cache = new Map<string, { data: any; expiry: number }>();
  
  set(key: string, data: any, ttl: number = 300000) { // 5分钟默认TTL
    const expiry = Date.now() + ttl;
    this.cache.set(key, { data, expiry });
  }
  
  get(key: string) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  clear() {
    this.cache.clear();
  }
}

const cache = new MemoryCache();

// API路由缓存中间件
function cacheMiddleware(ttl: number = 300000) {
  return (req: Request, res: Response, next: NextFunction) => {
    const key = `${req.method}:${req.originalUrl}`;
    const cached = cache.get(key);
    
    if (cached) {
      return res.json(cached);
    }
    
    const originalSend = res.json;
    res.json = function(data) {
      cache.set(key, data, ttl);
      return originalSend.call(this, data);
    };
    
    next();
  };
}
```

### 3. 响应压缩
```typescript
import compression from 'compression';
import express from 'express';

const app = express();

// Gzip压缩
app.use(compression({
  level: 6, // 压缩级别 (1-9)
  threshold: 1024, // 只压缩大于1KB的响应
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
}));

// 静态资源压缩
app.use('/static', express.static('public', {
  maxAge: '1y',
  etag: true,
  lastModified: true,
  setHeaders: (res, path) => {
    if (path.endsWith('.js') || path.endsWith('.css')) {
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
    }
  },
}));
```

## 服务器配置优化

### 1. Nginx配置
```nginx
# /etc/nginx/sites-available/cryptovault
server {
    listen 80;
    server_name your-domain.com;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 静态资源缓存
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status "STATIC";
    }
    
    location /images/ {
        expires 30d;
        add_header Cache-Control "public";
        add_header X-Cache-Status "IMAGE";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # API响应缓存
        proxy_cache api_cache;
        proxy_cache_valid 200 5m;
        proxy_cache_key "$scheme$request_method$host$request_uri";
        add_header X-Cache-Status $upstream_cache_status;
    }
    
    # 前端应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}

# 缓存配置
proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=100m inactive=60m use_temp_path=off;
```

### 2. PM2配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'cryptovault-frontend',
      script: 'npm',
      args: 'start',
      cwd: './frontend',
      instances: 1, // 单实例，节省内存
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      max_memory_restart: '500M',
      node_args: '--max-old-space-size=512', // 限制内存使用
    },
    {
      name: 'cryptovault-backend',
      script: 'dist/index.js',
      cwd: './backend',
      instances: 2, // 利用2核CPU
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
      },
      max_memory_restart: '300M',
      node_args: '--max-old-space-size=256',
    },
  ],
};
```

### 3. 系统级优化
```bash
# 系统优化脚本
#!/bin/bash

# 调整系统参数
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 1024' >> /etc/sysctl.conf
echo 'vm.swappiness = 10' >> /etc/sysctl.conf

# 应用配置
sysctl -p

# 设置文件描述符限制
echo '* soft nofile 65536' >> /etc/security/limits.conf
echo '* hard nofile 65536' >> /etc/security/limits.conf

# 优化磁盘I/O
echo 'deadline' > /sys/block/sda/queue/scheduler
```

## 监控和性能测试

### 1. 性能监控
```typescript
// 性能监控中间件
function performanceMonitor() {
  return (req: Request, res: Response, next: NextFunction) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      console.log(`${req.method} ${req.originalUrl} - ${duration}ms`);
      
      // 记录慢查询
      if (duration > 1000) {
        console.warn(`Slow request: ${req.method} ${req.originalUrl} - ${duration}ms`);
      }
    });
    
    next();
  };
}

// 内存使用监控
setInterval(() => {
  const used = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(used.rss / 1024 / 1024) + 'MB',
    heapTotal: Math.round(used.heapTotal / 1024 / 1024) + 'MB',
    heapUsed: Math.round(used.heapUsed / 1024 / 1024) + 'MB',
  });
}, 30000); // 每30秒检查一次
```

### 2. 前端性能测试
```typescript
// Web Vitals监控
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  // 发送到分析服务
  fetch('/api/analytics', {
    method: 'POST',
    body: JSON.stringify(metric),
  });
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

## 预期性能指标

### 优化前后对比
| 指标 | 优化前 | 优化后 | 改善 |
|------|--------|--------|------|
| 首屏加载 | 5-8秒 | 1.5-2秒 | 70%+ |
| 页面切换 | 1-2秒 | 200-500ms | 75%+ |
| API响应 | 500ms+ | 100-200ms | 60%+ |
| 包大小 | 2MB+ | 800KB | 60%+ |
| 内存使用 | 1.5GB+ | 800MB | 45%+ |
| CPU使用 | 90%+ | 60-70% | 25%+ |

### 目标达成策略
1. **分阶段优化**: 先基础优化，再深度优化
2. **持续监控**: 实时性能监控和告警
3. **用户反馈**: 收集真实用户体验数据
4. **定期优化**: 每月性能审查和优化

---

*性能优化方案版本: v1.0*
*创建时间: 2024-12-19*
*最后更新: 2024-12-19*
