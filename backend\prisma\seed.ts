import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 开始数据库种子数据初始化...');

  // 清理现有数据（开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log('🧹 清理现有数据...');
    await prisma.airdropClaim.deleteMany();
    await prisma.airdropCampaign.deleteMany();
    await prisma.nftHolding.deleteMany();
    await prisma.nft.deleteMany();
    await prisma.nftCollection.deleteMany();
    await prisma.stakingRecord.deleteMany();
    await prisma.stakingPool.deleteMany();
    await prisma.transaction.deleteMany();
    await prisma.token.deleteMany();
    await prisma.user.deleteMany();
    await prisma.systemConfig.deleteMany();
  }

  // 1. 创建系统配置
  console.log('⚙️ 创建系统配置...');
  await prisma.systemConfig.createMany({
    data: [
      {
        key: 'app_name',
        value: 'CryptoVault',
        description: '应用名称',
      },
      {
        key: 'app_version',
        value: '1.0.0',
        description: '应用版本',
      },
      {
        key: 'default_theme',
        value: 'electric-minimal',
        description: '默认主题',
      },
      {
        key: 'default_language',
        value: 'zh-CN',
        description: '默认语言',
      },
      {
        key: 'supported_chains',
        value: JSON.stringify([1, 56, 137, 42161]),
        description: '支持的区块链网络',
      },
    ],
  });

  // 2. 创建代币信息
  console.log('🪙 创建代币信息...');
  const cvtToken = await prisma.token.create({
    data: {
      address: '******************************************',
      chainId: 1,
      name: 'CryptoVault Token',
      symbol: 'CVT',
      decimals: 18,
      totalSupply: '1000000000000000000000000000', // 1B tokens
      price: '0.1',
      marketCap: '100000000',
      volume24h: '1000000',
      logoUrl: '/images/tokens/cvt.png',
      description: 'CryptoVault平台的原生治理代币',
      website: 'https://cryptovault.example.com',
      isVerified: true,
      isActive: true,
    },
  });

  // 创建其他常见代币
  const ethToken = await prisma.token.create({
    data: {
      address: '******************************************',
      chainId: 1,
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18,
      totalSupply: '120000000000000000000000000', // ~120M ETH
      price: '2000',
      marketCap: '240000000000',
      volume24h: '10000000000',
      logoUrl: '/images/tokens/eth.png',
      description: '以太坊原生代币',
      isVerified: true,
      isActive: true,
    },
  });

  const usdcToken = await prisma.token.create({
    data: {
      address: '******************************************',
      chainId: 1,
      name: 'USD Coin',
      symbol: 'USDC',
      decimals: 6,
      totalSupply: '50000000000000000', // 50B USDC
      price: '1',
      marketCap: '50000000000',
      volume24h: '5000000000',
      logoUrl: '/images/tokens/usdc.png',
      description: '美元稳定币',
      isVerified: true,
      isActive: true,
    },
  });

  // 3. 创建质押池
  console.log('💰 创建质押池...');
  const stakingPool = await prisma.stakingPool.create({
    data: {
      name: 'CVT质押池',
      description: '质押CVT代币获得奖励',
      stakingTokenId: cvtToken.id,
      rewardTokenId: cvtToken.id,
      apy: 12.5,
      totalStaked: '0',
      totalRewards: '0',
      lockPeriod: 0, // 无锁定期
      minStakeAmount: '100000000000000000000', // 100 CVT
      contractAddress: '******************************************',
      chainId: 1,
      isActive: true,
      startTime: new Date(),
    },
  });

  // 4. 创建NFT集合
  console.log('🖼️ 创建NFT集合...');
  const nftCollection = await prisma.nftCollection.create({
    data: {
      address: '******************************************',
      chainId: 1,
      name: 'CryptoVault Genesis',
      symbol: 'CVG',
      description: 'CryptoVault平台的创世NFT集合',
      imageUrl: '/images/nft/cvg-banner.png',
      website: 'https://cryptovault.example.com/nft',
      totalSupply: 10000,
      floorPrice: '0.1',
      isVerified: true,
      isActive: true,
    },
  });

  // 创建示例NFT
  const nfts = [];
  for (let i = 1; i <= 10; i++) {
    const nft = await prisma.nft.create({
      data: {
        tokenId: i.toString(),
        collectionId: nftCollection.id,
        name: `CryptoVault Genesis #${i}`,
        description: `CryptoVault Genesis NFT #${i}`,
        imageUrl: `/images/nft/cvg-${i}.png`,
        attributes: [
          { trait_type: 'Rarity', value: i <= 3 ? 'Legendary' : i <= 6 ? 'Rare' : 'Common' },
          { trait_type: 'Power', value: Math.floor(Math.random() * 100) + 1 },
          { trait_type: 'Element', value: ['Fire', 'Water', 'Earth', 'Air'][Math.floor(Math.random() * 4)] },
        ],
        rarityRank: i,
        rarityScore: 100 - i * 10,
      },
    });
    nfts.push(nft);
  }

  // 5. 创建空投活动
  console.log('🎁 创建空投活动...');
  const airdropCampaign = await prisma.airdropCampaign.create({
    data: {
      name: 'CryptoVault Genesis空投',
      description: '为早期用户提供的CVT代币空投',
      tokenAddress: cvtToken.address,
      chainId: 1,
      totalAmount: '1000000000000000000000000', // 1M CVT
      startTime: new Date(),
      endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
      claimDeadline: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60天后
      contractAddress: '******************************************',
      merkleRoot: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
      eligibilityRules: {
        minBalance: '1000000000000000000', // 1 ETH
        minTransactions: 10,
        whitelistRequired: false,
      },
      isActive: true,
    },
  });

  // 6. 创建测试用户
  console.log('👤 创建测试用户...');
  const testUser = await prisma.user.create({
    data: {
      address: '******************************************',
      ensName: 'testuser.eth',
      avatar: '/images/avatars/default.png',
      theme: 'electric-minimal',
      language: 'zh-CN',
      notifications: true,
    },
  });

  // 为测试用户创建一些数据
  // 质押记录
  await prisma.stakingRecord.create({
    data: {
      userId: testUser.id,
      poolId: stakingPool.id,
      amount: '1000000000000000000000', // 1000 CVT
      rewardDebt: '0',
      pendingRewards: '50000000000000000000', // 50 CVT
      stakeTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前
      isActive: true,
    },
  });

  // NFT持有记录
  await prisma.nftHolding.create({
    data: {
      userId: testUser.id,
      nftId: nfts[0].id,
      quantity: 1,
      acquiredPrice: '0.1',
      acquiredTxHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
    },
  });

  // 空投领取记录
  await prisma.airdropClaim.create({
    data: {
      userId: testUser.id,
      campaignId: airdropCampaign.id,
      amount: '1000000000000000000000', // 1000 CVT
      status: 'ELIGIBLE',
    },
  });

  // 交易记录
  await prisma.transaction.create({
    data: {
      hash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
      fromAddress: '******************************************',
      toAddress: testUser.address,
      value: '1000000000000000000000', // 1000 CVT
      gasUsed: '21000',
      gasPrice: '20000000000', // 20 Gwei
      userId: testUser.id,
      tokenId: cvtToken.id,
      chainId: 1,
      blockNumber: BigInt(18000000),
      blockHash: '0xdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abc',
      type: 'TRANSFER',
      status: 'CONFIRMED',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
    },
  });

  console.log('✅ 数据库种子数据初始化完成！');
  console.log(`📊 创建的数据统计:`);
  console.log(`   - 用户: 1个`);
  console.log(`   - 代币: 3个`);
  console.log(`   - 质押池: 1个`);
  console.log(`   - NFT集合: 1个`);
  console.log(`   - NFT: 10个`);
  console.log(`   - 空投活动: 1个`);
  console.log(`   - 系统配置: 5个`);
}

main()
  .catch((e) => {
    console.error('❌ 种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
