import type { AccessList, AccessListBuffer } from './types';
import type { Common } from '@ethereumjs/common';
export declare function checkMaxInitCodeSize(common: Common, length: number): void;
export declare class AccessLists {
    static getAccessListData(accessList: AccessListBuffer | AccessList): {
        AccessListJSON: AccessList;
        accessList: AccessListBuffer;
    };
    static verifyAccessList(accessList: AccessListBuffer): void;
    static getAccessListJSON(accessList: AccessListBuffer): any[];
    static getDataFeeEIP2930(accessList: AccessListBuffer, common: Common): number;
}
//# sourceMappingURL=util.d.ts.map