{"version": 3, "file": "eip1559Transaction.js", "sourceRoot": "", "sources": ["../src/eip1559Transaction.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AACrC,2CAUyB;AACzB,yDAAwD;AAExD,uDAAmD;AACnD,iCAAoC;AAYpC,MAAM,gBAAgB,GAAG,CAAC,CAAA;AAC1B,MAAM,uBAAuB,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;AAElG;;;;;GAKG;AACH,MAAa,2BAA4B,SAAQ,iCAA4C;IAyG3F;;;;;;OAMG;IACH,YAAmB,MAA8B,EAAE,OAAkB,EAAE;QACrE,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,IAAI,CAAC,CAAA;QAxGpD;;;;;WAKG;QACO,qBAAgB,GAAG,QAAQ,CAAA;QAmGnC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAAG,MAAM,CAAA;QAE1E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QAEpC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;YAC9C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAE5E,kCAAkC;QAClC,MAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,kBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,IAAI,CAAC,YAAY,GAAG,IAAA,qBAAc,EAAC,IAAA,eAAQ,EAAC,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAA;QACvF,IAAI,CAAC,oBAAoB,GAAG,IAAA,qBAAc,EACxC,IAAA,eAAQ,EAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,CACpE,CAAA;QAED,IAAI,CAAC,+BAA+B,CAAC;YACnC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;SAChD,CAAC,CAAA;QAEF,iCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,kBAAW,EAAE;YACnD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,6DAA6D,CAAC,CAAA;YACzF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,iGAAiG,CAClG,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACvB,IAAI,CAAC,cAAc,EAAE,CAAA;QAErB,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IAjJD;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAA8B,EAAE,OAAkB,EAAE;QAC3E,OAAO,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACtD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAkB,EAAE,OAAkB,EAAE;QACrE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,uBAAuB,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CACb,sFAAsF,gBAAgB,eAAe,UAAU;iBAC5H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;iBACX,QAAQ,CAAC,KAAK,CAAC,EAAE,CACrB,CAAA;SACF;QAED,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,SAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAE3D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,2BAA2B,CAAC,eAAe,CAAC,MAAa,EAAE,IAAI,CAAC,CAAA;IACzE,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAmC,EAAE,OAAkB,EAAE;QACrF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAA;SACF;QAED,MAAM,CACJ,OAAO,EACP,KAAK,EACL,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,CAAC,EACD,CAAC,EACD,CAAC,EACF,GAAG,MAAM,CAAA;QAEV,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACtC,IAAA,8BAAuB,EAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEhG,OAAO,IAAI,2BAA2B,CACpC;YACE,OAAO,EAAE,IAAA,qBAAc,EAAC,OAAO,CAAC;YAChC,KAAK;YACL,oBAAoB;YACpB,YAAY;YACZ,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,IAAI,EAAE;YAC5B,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,qBAAc,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAClD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IA6DD;;OAEG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAChF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;SAChC;QAED,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE,CAAA;QAC7B,IAAI,IAAI,MAAM,CAAC,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QAE3E,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aACjC,CAAA;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,UAAkB,MAAM,CAAC,CAAC,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAA;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO,CAAA;QAC3C,MAAM,kBAAkB,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAA;QAC1D,MAAM,QAAQ,GAAG,kBAAkB,GAAG,OAAO,CAAA;QAC7C,OAAO,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAA;IAC9C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,6BAAsB,EAAC,IAAI,CAAC,OAAO,CAAC;YACpC,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAA,6BAAsB,EAAC,IAAI,CAAC,oBAAoB,CAAC;YACjD,IAAA,6BAAsB,EAAC,IAAI,CAAC,YAAY,CAAC;YACzC,IAAA,6BAAsB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SACxE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS;QACP,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACvB,OAAO,MAAM,CAAC,MAAM,CAAC;YACnB,uBAAuB;YACvB,MAAM,CAAC,IAAI,CAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAgB,CAAC,CAAC,CAAC;SACvD,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACnC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YAC5B,uBAAuB;YACvB,MAAM,CAAC,IAAI,CAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAgB,CAAC,CAAC,CAAC;SACvD,CAAC,CAAA;QACF,IAAI,WAAW,EAAE;YACf,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,OAAO,CAAC,CAAC,CAAA;SACvC;aAAM;YACL,OAAO,OAAO,CAAA;SACf;IACH,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;aAC3D;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;SACvB;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAChC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAClD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAA;QAExB,IAAI,CAAC,cAAc,EAAE,CAAA;QAErB,IAAI;YACF,OAAO,IAAA,gBAAS,EACd,OAAO,EACP,CAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,gDAAgD;YACjE,IAAA,6BAAsB,EAAC,CAAE,CAAC,EAC1B,IAAA,6BAAsB,EAAC,CAAE,CAAC,CAC3B,CAAA;SACF;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED,iBAAiB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/C,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,2BAA2B,CAAC,UAAU,CAC3C;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;YACjB,CAAC,EAAE,IAAA,qBAAc,EAAC,CAAC,CAAC;YACpB,CAAC,EAAE,IAAA,qBAAc,EAAC,CAAC,CAAC;SACrB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAErE,OAAO;YACL,OAAO,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,OAAO,CAAC;YAClC,KAAK,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,oBAAoB,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5D,YAAY,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,YAAY,CAAC;YAC5C,QAAQ,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtC,UAAU,EAAE,cAAc;YAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SAC1D,CAAA;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,iBAAiB,IAAI,CAAC,YAAY,yBAAyB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClG,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAA;IACtC,CAAC;CACF;AArYD,kEAqYC"}