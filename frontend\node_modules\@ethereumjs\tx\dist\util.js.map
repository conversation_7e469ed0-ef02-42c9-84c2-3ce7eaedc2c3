{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";;;AAAA,2CAAuE;AAEvE,mCAAsC;AAKtC,SAAgB,oBAAoB,CAAC,MAAc,EAAE,MAAc;IACjE,MAAM,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;IAC7D,IAAI,eAAe,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE;QACvD,MAAM,IAAI,KAAK,CACb,6DAA6D,MAAM,qBAAqB,MAAM,CAAC,KAAK,CAClG,IAAI,EACJ,iBAAiB,CAClB,EAAE,CACJ,CAAA;KACF;AACH,CAAC;AAVD,oDAUC;AAED,MAAa,WAAW;IACf,MAAM,CAAC,iBAAiB,CAAC,UAAyC;QACvE,IAAI,cAAc,CAAA;QAClB,IAAI,gBAAgB,CAAA;QACpB,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,EAAE;YAC5B,cAAc,GAAG,UAAU,CAAA;YAC3B,MAAM,aAAa,GAAqB,EAAE,CAAA;YAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,IAAI,GAAmB,UAAU,CAAC,CAAC,CAAC,CAAA;gBAC1C,MAAM,aAAa,GAAG,IAAA,eAAQ,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC5C,MAAM,YAAY,GAAa,EAAE,CAAA;gBACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC5D,YAAY,CAAC,IAAI,CAAC,IAAA,eAAQ,EAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;iBACrD;gBACD,aAAa,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAA;aAClD;YACD,gBAAgB,GAAG,aAAa,CAAA;SACjC;aAAM;YACL,gBAAgB,GAAG,UAAU,IAAI,EAAE,CAAA;YACnC,iBAAiB;YACjB,MAAM,IAAI,GAAe,EAAE,CAAA;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,MAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAA;gBAChC,MAAM,OAAO,GAAG,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACpC,MAAM,WAAW,GAAa,EAAE,CAAA;gBAChC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;oBAChD,WAAW,CAAC,IAAI,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBAC7C;gBACD,MAAM,QAAQ,GAAmB;oBAC/B,OAAO;oBACP,WAAW;iBACZ,CAAA;gBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;aACpB;YACD,cAAc,GAAG,IAAI,CAAA;SACtB;QAED,OAAO;YACL,cAAc;YACd,UAAU,EAAE,gBAAgB;SAC7B,CAAA;IACH,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,UAA4B;QACzD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YAChD,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;YACtC,MAAM,OAAO,GAAW,cAAc,CAAC,CAAC,CAAC,CAAA;YACzC,MAAM,YAAY,GAAa,cAAc,CAAC,CAAC,CAAC,CAAA;YAChD,IAAU,cAAe,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC1C,MAAM,IAAI,KAAK,CACb,sGAAsG,CACvG,CAAA;aACF;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAA;aACnF;YACD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBAC1E,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;oBAC3C,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAA;iBACxF;aACF;SACF;IACH,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,UAA4B;QAC1D,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,IAAI,GAAQ,UAAU,CAAC,KAAK,CAAC,CAAA;YACnC,MAAM,QAAQ,GAAQ;gBACpB,OAAO,EAAE,IAAI,GAAG,IAAA,oBAAa,EAAS,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAClE,WAAW,EAAE,EAAE;aAChB,CAAA;YACD,MAAM,YAAY,GAAa,IAAI,CAAC,CAAC,CAAC,CAAA;YACtC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gBACrD,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;gBACtC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,IAAA,oBAAa,EAAC,WAAW,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;aACjF;YACD,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC9B;QACD,OAAO,cAAc,CAAA;IACvB,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,UAA4B,EAAE,MAAc;QAC1E,MAAM,wBAAwB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAA;QACtF,MAAM,qBAAqB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAA;QAEhF,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAC5B,KAAK,IAAI,YAAY,CAAC,MAAM,CAAA;SAC7B;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAA;QACnC,OAAO,SAAS,GAAG,MAAM,CAAC,qBAAqB,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAA;IAC7F,CAAC;CACF;AAjGD,kCAiGC"}