import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
}));

// 请求日志
app.use(morgan('combined'));

// 请求体解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 限流配置
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 限制每个IP 100个请求
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'CryptoVault API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  });
});

// API路由
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    data: {
      server: 'online',
      database: 'disconnected', // 暂时未连接数据库
      cache: 'disabled',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString(),
    },
  });
});

// 系统信息端点
app.get('/api/system', (req, res) => {
  res.json({
    success: true,
    data: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      cpuUsage: process.cpuUsage(),
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
    },
  });
});

// 模拟代币信息API
app.get('/api/tokens/info', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'CryptoVault Token',
      symbol: 'CVT',
      decimals: 18,
      totalSupply: '1000000000',
      price: '0.00',
      marketCap: '0',
      volume24h: '0',
      holders: 0,
      lastUpdated: new Date().toISOString(),
    },
  });
});

// 模拟质押池API
app.get('/api/staking/pools', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'cvt-pool-1',
        name: 'CVT Staking Pool',
        stakingToken: 'CVT',
        rewardToken: 'CVT',
        apy: 12.5,
        totalStaked: '0',
        totalRewards: '0',
        lockPeriod: 0,
        isActive: true,
      },
    ],
  });
});

// 模拟用户配置API
app.get('/api/users/profile', (req, res) => {
  // 在实际应用中，这里会验证JWT token
  res.json({
    success: true,
    data: {
      address: '******************************************',
      ensName: null,
      avatar: null,
      preferences: {
        theme: 'electric-minimal',
        language: 'zh-CN',
        notifications: true,
      },
      firstConnected: new Date().toISOString(),
      lastActive: new Date().toISOString(),
    },
  });
});

// Web Vitals分析端点
app.post('/api/analytics/web-vitals', (req, res) => {
  const { name, value, id } = req.body;
  
  // 在实际应用中，这里会保存到数据库
  console.log(`Web Vital: ${name} = ${value} (${id})`);
  
  res.json({
    success: true,
    message: 'Web vital recorded',
  });
});

// 错误分析端点
app.post('/api/analytics/errors', (req, res) => {
  const { message, stack, url, userAgent } = req.body;
  
  // 在实际应用中，这里会保存到数据库
  console.error('Frontend Error:', { message, stack, url, userAgent });
  
  res.json({
    success: true,
    message: 'Error recorded',
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'API endpoint not found',
      path: req.originalUrl,
    },
  });
});

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', err);
  
  res.status(err.status || 500).json({
    success: false,
    error: {
      code: err.code || 'INTERNAL_SERVER_ERROR',
      message: process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : err.message,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
    },
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 CryptoVault API Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📈 Status: http://localhost:${PORT}/api/status`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
