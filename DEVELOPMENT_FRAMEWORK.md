# CryptoVault 开发框架与规则

## 开发框架概述

建立一套标准化的开发框架，确保代码质量、开发效率和项目可维护性，同时为后续类似项目提供可复用的基础架构。

## 项目架构原则

### 1. 模块化设计
- **功能模块独立**: 每个功能模块可独立开发和测试
- **接口标准化**: 统一的API接口规范
- **组件复用**: 高度可复用的UI组件库
- **配置驱动**: 通过配置文件控制功能开关

### 2. 可扩展性
- **插件化架构**: 支持功能插件动态加载
- **主题系统**: 可配置的UI主题切换
- **多语言支持**: 完整的国际化框架
- **数据库抽象**: 支持多种数据库后端

### 3. 性能优先
- **代码分割**: 按需加载减少初始包大小
- **缓存策略**: 多层缓存提升响应速度
- **优化构建**: 生产环境优化配置
- **监控体系**: 实时性能监控和报警

## 代码规范

### TypeScript规范
```typescript
// 1. 严格类型定义
interface UserProfile {
  id: string;
  address: string;
  username?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 2. 泛型使用
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 3. 枚举定义
enum TransactionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
}

// 4. 工具类型
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
type CreateUserRequest = Optional<UserProfile, 'id' | 'createdAt' | 'updatedAt'>;
```

### 组件规范
```typescript
// 1. 组件接口定义
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

// 2. 组件实现
export function Button({ 
  variant = 'primary', 
  size = 'md', 
  disabled = false,
  loading = false,
  onClick,
  children 
}: ButtonProps) {
  // 组件逻辑
  return (
    <button 
      className={getButtonClasses(variant, size)}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading ? <Spinner /> : children}
    </button>
  );
}

// 3. 样式函数
function getButtonClasses(variant: string, size: string): string {
  const baseClasses = 'font-medium rounded-theme-md transition-colors';
  const variantClasses = {
    primary: 'bg-primary text-background hover:bg-primary-hover',
    secondary: 'bg-surface text-text-primary hover:bg-surface-hover',
    outline: 'border border-border text-text-primary hover:bg-surface',
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };
  
  return `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`;
}
```

### API规范
```typescript
// 1. 路由结构
// GET    /api/v1/users/:id
// POST   /api/v1/users
// PUT    /api/v1/users/:id
// DELETE /api/v1/users/:id

// 2. 响应格式
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 3. 错误处理
class ApiError extends Error {
  constructor(
    public code: string,
    public message: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}
```

## 文件组织规范

### 前端文件结构
```
frontend/
├── app/                          # Next.js App Router
│   ├── [locale]/                # 多语言路由
│   │   ├── (dashboard)/         # 路由组
│   │   │   ├── dashboard/       # 仪表板页面
│   │   │   ├── wallet/          # 钱包页面
│   │   │   └── layout.tsx       # 布局组件
│   │   ├── globals.css          # 全局样式
│   │   └── layout.tsx           # 根布局
│   └── api/                     # API路由
├── components/                   # 组件库
│   ├── ui/                      # 基础UI组件
│   │   ├── Button/              # 按钮组件
│   │   │   ├── Button.tsx       # 组件实现
│   │   │   ├── Button.test.tsx  # 组件测试
│   │   │   └── index.ts         # 导出文件
│   │   └── index.ts             # UI组件统一导出
│   ├── web3/                    # Web3相关组件
│   ├── layout/                  # 布局组件
│   └── features/                # 功能组件
├── lib/                         # 工具库
│   ├── utils/                   # 通用工具函数
│   ├── hooks/                   # 自定义Hooks
│   ├── contexts/                # React Context
│   ├── types/                   # TypeScript类型
│   └── constants/               # 常量定义
├── styles/                      # 样式文件
└── messages/                    # 多语言文件
```

### 后端文件结构
```
backend/
├── src/
│   ├── routes/                  # 路由定义
│   │   ├── auth/                # 认证路由
│   │   ├── users/               # 用户路由
│   │   ├── tokens/              # 代币路由
│   │   └── index.ts             # 路由汇总
│   ├── controllers/             # 控制器
│   ├── services/                # 业务逻辑
│   ├── models/                  # 数据模型
│   ├── middleware/              # 中间件
│   ├── utils/                   # 工具函数
│   └── types/                   # 类型定义
├── prisma/                      # 数据库
│   ├── schema.prisma            # 数据库模式
│   ├── migrations/              # 迁移文件
│   └── seed.ts                  # 种子数据
└── tests/                       # 测试文件
```

## 开发流程

### 1. 功能开发流程
```mermaid
graph TD
    A[需求分析] --> B[技术设计]
    B --> C[接口设计]
    C --> D[数据库设计]
    D --> E[前端开发]
    E --> F[后端开发]
    F --> G[集成测试]
    G --> H[代码审查]
    H --> I[部署测试]
    I --> J[功能验收]
```

### 2. Git工作流
```bash
# 功能分支命名规范
feature/wallet-connection      # 新功能
bugfix/login-error            # 错误修复
hotfix/security-patch         # 紧急修复
refactor/api-structure        # 重构

# 提交信息规范
feat: 添加钱包连接功能
fix: 修复登录错误
docs: 更新API文档
style: 代码格式化
refactor: 重构用户服务
test: 添加单元测试
chore: 更新依赖包
```

### 3. 代码审查清单
- [ ] 代码符合TypeScript规范
- [ ] 组件具有适当的类型定义
- [ ] 错误处理完整
- [ ] 性能考虑（避免不必要的重渲染）
- [ ] 安全性检查（输入验证、XSS防护）
- [ ] 测试覆盖率达标
- [ ] 文档更新

## 测试策略

### 1. 测试金字塔
```
    /\
   /  \     E2E Tests (少量)
  /____\    
 /      \   Integration Tests (适量)
/________\  Unit Tests (大量)
```

### 2. 测试类型
```typescript
// 单元测试 - 组件测试
describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});

// 集成测试 - API测试
describe('User API', () => {
  test('should create user successfully', async () => {
    const userData = { username: 'test', email: '<EMAIL>' };
    const response = await request(app)
      .post('/api/v1/users')
      .send(userData)
      .expect(201);
    
    expect(response.body.success).toBe(true);
    expect(response.body.data.username).toBe('test');
  });
});

// E2E测试 - 用户流程测试
test('user can connect wallet and view dashboard', async ({ page }) => {
  await page.goto('/');
  await page.click('[data-testid="connect-wallet"]');
  await page.waitForSelector('[data-testid="wallet-connected"]');
  await page.click('[data-testid="dashboard-link"]');
  await expect(page.locator('h1')).toContainText('Dashboard');
});
```

## 性能监控

### 1. 前端性能指标
- **Core Web Vitals**: LCP, FID, CLS
- **加载性能**: TTFB, FCP, TTI
- **运行时性能**: 内存使用、CPU占用
- **用户体验**: 错误率、崩溃率

### 2. 后端性能指标
- **响应时间**: API平均响应时间
- **吞吐量**: 每秒请求数
- **错误率**: 4xx/5xx错误比例
- **资源使用**: CPU、内存、磁盘

### 3. 监控工具集成
```typescript
// 性能监控配置
export const performanceConfig = {
  // Web Vitals监控
  webVitals: {
    enabled: true,
    reportUrl: '/api/analytics/web-vitals',
  },
  
  // 错误监控
  errorTracking: {
    enabled: true,
    reportUrl: '/api/analytics/errors',
  },
  
  // 用户行为分析
  analytics: {
    enabled: true,
    trackPageViews: true,
    trackUserInteractions: true,
  },
};
```

## 部署策略

### 1. 环境配置
```bash
# 开发环境
NODE_ENV=development
DATABASE_URL=sqlite:./dev.db
NEXT_PUBLIC_API_URL=http://localhost:3001

# 测试环境
NODE_ENV=test
DATABASE_URL=sqlite:./test.db
NEXT_PUBLIC_API_URL=http://test-api.example.com

# 生产环境
NODE_ENV=production
DATABASE_URL=********************************/db
NEXT_PUBLIC_API_URL=https://api.example.com
```

### 2. 构建优化
```javascript
// next.config.js
module.exports = {
  // 生产环境优化
  swcMinify: true,
  compress: true,
  
  // 图片优化
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // 实验性功能
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['prisma'],
  },
};
```

### 3. 服务器配置
```nginx
# Nginx配置示例
server {
    listen 80;
    server_name example.com;
    
    # 静态资源缓存
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 前端应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

*开发框架文档版本: v1.0*
*创建时间: 2024-12-19*
*最后更新: 2024-12-19*
