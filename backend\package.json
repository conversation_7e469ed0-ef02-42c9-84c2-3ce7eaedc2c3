{"name": "cryptovault-backend", "version": "0.1.0", "private": true, "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "db:init": "node scripts/init-database.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force && npm run db:seed"}, "dependencies": {"@prisma/client": "^5.22.0", "bcryptjs": "^2.4.0", "cors": "^2.8.0", "dotenv": "^16.3.0", "express": "^4.18.0", "express-rate-limit": "^7.1.0", "express-validator": "^7.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.0", "jest": "^29.7.0", "nodemon": "^3.0.0", "prisma": "^5.6.0", "supertest": "^6.3.0", "tsx": "^4.6.0", "typescript": "^5.0.0"}}